import { Grid, GridItem } from '@chakra-ui/react';
import React, { ReactNode } from 'react';
import SidebarSecondary from '../../components/SidebarSecondary';
import { appPaths } from '../../constants/app-paths';
import {
  FiSettings,
  FiColumns,
  FiTag,
  FiUser,
  FiLink,
  FiClipboard,
  FiClock,
  FiFileText,
  FiBriefcase,
  FiMessageSquare,
} from 'react-icons/fi';
import { MdOutlineMail } from 'react-icons/md';
import { IconType } from 'react-icons/lib';

interface SettingsLayoutProps {
  children: ReactNode;
}

const SIDEBAR_OPTIONS: { title: string; path: string; icon: IconType }[] = [
  {
    title: 'Geral',
    path: appPaths.settings.general(),
    icon: FiSettings,
  },
  {
    title: 'Criar & Ocultar Colunas',
    path: appPaths.settings.customColumns(),
    icon: FiColumns,
  },
  {
    title: 'Tags',
    path: appPaths.settings.tags(),
    icon: FiTag,
  },
  {
    title: 'Configurações de conta',
    path: appPaths.settings.accountSettings(),
    icon: FiUser,
  },
  {
    title: 'Integrações',
    path: appPaths.settings.integrationSettings.index(),
    icon: FiLink,
  },
  {
    title: 'Email',
    path: appPaths.settings.email(),
    icon: MdOutlineMail,
  },
  {
    title: 'Respostas rápidas',
    path: appPaths.settings.quickReplies(),
    icon: FiClipboard,
  },
  {
    title: 'Horário de Atendimento',
    path: appPaths.settings.businessHours(),
    icon: FiClock,
  },
  {
    title: 'Gerenciar Cargos',
    path: appPaths.settings.rolesManagement.index(),
    icon: FiBriefcase,
  },
  {
    title: 'Gerenciar Setores de Conversa',
    path: appPaths.settings.conversationSectorsManagement.index(),
    icon: FiMessageSquare,
  },
  {
    title: 'Faturas',
    path: appPaths.settings.invoices(),
    icon: FiFileText,
  },
];

const SettingsLayout = ({ children }: SettingsLayoutProps) => {
  return (
    <Grid
      height={'100vh'}
      templateColumns="250px auto"
      templateAreas={'"sidebar page"'}
    >
      <GridItem area="sidebar">
        <SidebarSecondary title="Configurações" options={SIDEBAR_OPTIONS} />
      </GridItem>
      <GridItem area="page" maxH={'100vh'} height={'100vh'} overflow="auto">
        {children}
      </GridItem>
    </Grid>
  );
};

export default SettingsLayout;
