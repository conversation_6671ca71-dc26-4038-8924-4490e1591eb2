import { Box, Flex, Image, Text, Icon, HStack, VStack } from '@chakra-ui/react';
import WhatsappMessageItem, {
  WhatsappMessageItemProps,
} from '../WhatsappMessageItem';
import { FaArrowLeft, FaEllipsisH, FaPhone, FaVideo } from 'react-icons/fa';
import { IoMdMic } from 'react-icons/io';
import { MdAttachFile, MdCameraAlt } from 'react-icons/md';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { phonePreview } from '../../constants/colors';
import { useSelector } from 'react-redux';
import { RootState } from '../../state/store';

interface WhatsappTemplatePreviewProps extends WhatsappMessageItemProps {
  height?: string;
  contactStatus?: string;
  contactAvatar?: string;
  messageStatus?: 'sent' | 'delivered' | 'read';
}

const WhatsappTemplatePreview = ({
  message,
  file,
  buttons,
  footer,
  fileUrl,
  height = '100vh',
  messageTemplateCards,
  limitedOfferText,
  limitedOfferExpirationDate,
  contactStatus = 'online',
  contactAvatar = '/logo512.png',
  messageStatus = 'read',
}: WhatsappTemplatePreviewProps) => {
  const companyName = useSelector(
    (state: RootState) => state.auth.currentUser?.company?.name,
  );

  const currentTime = format(new Date(), 'HH:mm', { locale: ptBR });

  return (
    <Box
      height={height}
      width="100%"
      maxWidth="405px"
      maxH="750px"
      minWidth="320px"
      borderRadius="2xl"
      overflow="hidden"
      boxShadow="0 8px 25px rgba(0, 0, 0, 0.2)"
      display="flex"
      flexDirection="column"
      position="relative"
      bg={phonePreview.chatBg}
      fontFamily="-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif"
    >
      <Box
        position="absolute"
        top="0"
        left="50%"
        transform="translateX(-50%)"
        width="35%"
        height="22px"
        bg="rgba(0,0,0,0.8)"
        borderBottomLeftRadius="12px"
        borderBottomRightRadius="12px"
        zIndex="10"
        boxShadow="0 1px 2px rgba(0,0,0,0.1)"
      />
      <Flex
        bg={phonePreview.statusBar}
        pt={6}
        pb={1}
        px={4}
        color="white"
        justifyContent="space-between"
        alignItems="center"
      >
        <Text fontSize="xs" fontWeight="medium">
          {currentTime}
        </Text>
        <HStack spacing={2}>
          <Icon as={FaPhone} fontSize="xs" />
          <Icon as={FaVideo} fontSize="xs" />
          <Text fontSize="xs">100%</Text>
        </HStack>
      </Flex>
      <Flex
        bg={phonePreview.headerBg}
        p={3}
        color="white"
        alignItems="center"
        justifyContent="space-between"
        boxShadow="0 1px 3px rgba(0,0,0,0.12)"
      >
        <Flex alignItems="center">
          <Icon as={FaArrowLeft} mr={3} />
          <Image
            src={contactAvatar}
            borderRadius="full"
            boxSize="40px"
            mr={3}
            border="1px solid rgba(255,255,255,0.2)"
          />
          <VStack alignItems="flex-start" spacing={0}>
            <Text fontWeight="bold" fontSize="md">
              {companyName?.slice(0, 8)}
            </Text>
            <Text fontSize="xs" color="rgba(255,255,255,0.8)">
              {contactStatus}
            </Text>
          </VStack>
        </Flex>
        <HStack spacing={5}>
          <Icon as={FaVideo} boxSize="18px" />
          <Icon as={FaPhone} boxSize="18px" />
          <Icon as={FaEllipsisH} boxSize="18px" />
        </HStack>
      </Flex>
      <Box
        flex="1"
        backgroundImage="url('/iphone-chat-bg.jpg')"
        backgroundSize="cover"
        padding={3}
        overflowY="auto"
        css={{
          '&::-webkit-scrollbar': {
            width: '3px',
          },
          '&::-webkit-scrollbar-track': {
            width: '3px',
          },
          '&::-webkit-scrollbar-thumb': {
            background: 'rgba(0,0,0,0.2)',
            borderRadius: '3px',
          },
        }}
      >
        <Flex justify="center" mb={4}>
          <Text
            fontSize="xs"
            bg="rgba(225, 245, 254, 0.8)"
            color="#263238"
            px={3}
            py={1}
            borderRadius="full"
            fontWeight="medium"
            boxShadow="0 1px 1px rgba(0,0,0,0.05)"
          >
            {format(new Date(), "EEEE, d 'de' MMMM", { locale: ptBR })}
          </Text>
        </Flex>

        <Flex justifyContent="flex-end" mb={2} ml={0}>
          <WhatsappMessageItem
            message={message}
            limitedOfferText={limitedOfferText}
            limitedOfferExpirationDate={limitedOfferExpirationDate}
            file={file}
            buttons={buttons}
            footer={footer}
            fileUrl={fileUrl}
            messageTemplateCards={messageTemplateCards}
            messageStatus={messageStatus}
            isPreview={true}
          />
        </Flex>
      </Box>

      <Flex
        bg="#F0F2F5"
        p={2}
        pb={4}
        alignItems="center"
        justifyContent="space-between"
        borderTopWidth="1px"
        borderTopColor="rgba(0,0,0,0.1)"
      >
        <Flex
          flex="1"
          mx={1}
          borderRadius="full"
          bg={phonePreview.inputBg}
          p={1}
          alignItems="center"
        >
          <Box borderRadius="full" p={2} color={phonePreview.iconColor}>
            <Icon as={MdCameraAlt} boxSize="20px" />
          </Box>

          <Box flex="1" py={1} px={2}>
            <Text fontSize="sm" color="gray.500">
              Mensagem
            </Text>
          </Box>

          <Box borderRadius="full" p={2} color={phonePreview.iconColor}>
            <Icon as={MdAttachFile} boxSize="20px" transform="rotate(45deg)" />
          </Box>
        </Flex>

        <Box
          bg={phonePreview.buttonColor}
          borderRadius="full"
          p={2}
          color="white"
          ml={1}
          transition="all 0.2s"
        >
          <Icon as={IoMdMic} boxSize="20px" />
        </Box>
      </Flex>

      <Box
        height="4px"
        width="25%"
        bg="rgba(0,0,0,0.7)"
        borderRadius="full"
        position="absolute"
        bottom="2px"
        left="50%"
        transform="translateX(-50%)"
      />
    </Box>
  );
};

export default WhatsappTemplatePreview;
