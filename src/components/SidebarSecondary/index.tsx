import { Box, Flex, Heading, Text, VStack, Icon } from '@chakra-ui/react';
import { useLocation, useNavigate } from 'react-router-dom';
import { colors } from '../../constants/colors';
import { hoverCardStyles } from '../../styles/hover.styles';
import { useAppModuleAccessGuard } from '../../hooks/useAppModuleAccessGuard';
import { scrollbarStyles } from '../../styles/scrollbar.styles';
import { IconType } from 'react-icons/lib';

interface SidebarSecondaryProps {
  title: string;
  options: {
    title: string;
    path: string;
    icon: IconType;
  }[];
}

const SidebarSecondary = ({ title, options }: SidebarSecondaryProps) => {
  const { checkUserHasPathAccess } = useAppModuleAccessGuard();
  const navigate = useNavigate();
  const location = useLocation();

  function isActivePath(path: string) {
    return location.pathname.includes(path);
  }

  return (
    <Flex
      flexDir="column"
      bgColor={'gray.50'}
      height="100%"
      borderLeft={`solid 1px ${colors.middleGrey}`}
      borderRight="none"
      boxShadow="2px 0 5px rgba(0, 0, 0, 0.1)"
      position="relative"
      overflowY="auto"
      css={scrollbarStyles({ background: 'white' })}
    >
      <Box
        paddingY={6}
        paddingX={4}
        borderBottom={`1px solid ${colors.lightGrey}`}
        position="sticky"
        top={0}
        bgColor="gray.50"
        zIndex={1}
      >
        <Heading
          size={'lg'}
          alignSelf="flex-start"
          bgGradient={`linear(to-r, ${colors.primary}, ${colors.primaryLight})`}
          bgClip="text"
        >
          {title}
        </Heading>
      </Box>

      <VStack spacing={0} width="100%" paddingY={2}>
        {options.map((option) => {
          if (!checkUserHasPathAccess(option.path)) return null;

          const isActive = isActivePath(option.path);

          return (
            <Flex
              padding={4}
              bgColor={isActive ? colors.blueLight : 'transparent'}
              _hover={{
                ...hoverCardStyles,
                bgColor: isActive
                  ? colors.blueLight
                  : 'rgba(255, 255, 255, 0.5)',
                transform: 'translateX(2px)',
              }}
              cursor={'pointer'}
              width="100%"
              onClick={() => navigate(option.path)}
              borderBottom={`solid 1px ${colors.lightGrey}`}
              gap={4}
              alignItems="center"
              transition="all 0.2s"
              role="group"
            >
              <Icon
                as={option.icon}
                boxSize={5}
                color={isActive ? colors.primary : colors.darkGrey}
                _groupHover={{ color: colors.primary }}
              />
              <Text
                fontWeight={isActive ? 'medium' : 'normal'}
                color={isActive ? colors.primary : colors.fontDark}
                _groupHover={{ color: colors.primary }}
              >
                {option.title}
              </Text>
            </Flex>
          );
        })}
      </VStack>
    </Flex>
  );
};

export default SidebarSecondary;
