import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Stack,
  Text,
  useToast,
  IconButton,
  Tooltip,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  useDisclosure,
} from '@chakra-ui/react';
import { ArrowLeftIcon } from '@chakra-ui/icons';
import { useMutation, useQuery } from 'react-query';
import { useNavigate, useParams } from 'react-router-dom';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { ConversationSectorsService } from '../../../../services/conversation-sectors.service';
import { apiRoutes } from '../../../../constants/api-routes';
import { colors } from '../../../../constants/colors';
import InputSelect, { SelectOption } from '../../../../components/InputSelect';
import { UsersService } from '../../../../services/users.service';
import { appPaths } from '../../../../constants/app-paths';
import { User } from '../../../../types/Prisma';
import {
  CreateConversationSectorDto,
  UpdateConversationSectorDto,
} from '../../../../types/ConversationSector';
import { omit } from 'lodash';
import LoadingScreen from '../../../../components/LoadingScreen';
import { ConversationSectorCategoriesManagement } from './components/ConversationSectorCategoriesManagement';
import { useState } from 'react';
import { DeleteConversationSectorDialog } from './components/DeleteConversationSectorDialog';

const schema = yup.object({
  name: yup.string().required('O nome é obrigatório'),
  userIds: yup.array().of(yup.string()),
});

type SchemaType = yup.InferType<typeof schema>;

export const ConversationSectorCrudPage = () => {
  const { conversationSectorId: id } = useParams<{
    conversationSectorId?: string;
  }>();
  const navigate = useNavigate();
  const toast = useToast();
  const isEditing = !!id;
  const [selectedTab, setSelectedTab] = useState<number>(0);
  const {
    isOpen: isDeleteDialogOpen,
    onOpen: onDeleteDialogOpen,
    onClose: onDeleteDialogClose,
  } = useDisclosure();

  const {
    handleSubmit,
    formState: { errors, isDirty },
    setValue,
    control,
  } = useForm<SchemaType>({
    resolver: yupResolver(schema),
    defaultValues: {
      name: '',
      userIds: [],
    },
  });

  const { data: usersOptions = [], isLoading: isUsersQueryLoading } = useQuery(
    apiRoutes.listCompanyAgents(),
    async () => {
      const { data } = await UsersService.listCompanyAgents();
      return data.map((user: User) => ({
        value: user.id,
        label: `${user.name} (${user.email})`,
      }));
    },
  );

  const {
    data: conversationSectorData,
    isLoading: isConversationSectorQueryLoading,
  } = useQuery(
    apiRoutes.getConversationSector(id!),
    async () => {
      const { data } = await ConversationSectorsService.getConversationSector(
        id!,
      );
      return data;
    },
    {
      enabled: isEditing,
      onSuccess: (data) => {
        setValue('name', data.name || '');

        setValue(
          'userIds',
          data.userConversationSectors.map((user) => user.userId),
        );
      },
    },
  );

  const createConversationSector = useMutation(
    async (args: CreateConversationSectorDto) => {
      const { data } =
        await ConversationSectorsService.createConversationSector(args);
      return data;
    },
    {
      onSuccess: (data) => {
        toast({
          title: 'Setor de Conversa criado com sucesso',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
        navigate(appPaths.settings.conversationSectorsManagement.edit(data.id));
      },
    },
  );

  const updateConversationSector = useMutation(
    (data: UpdateConversationSectorDto) =>
      ConversationSectorsService.updateConversationSector(id!, data),
    {
      onSuccess: () => {
        toast({
          title: 'Setor de Conversa atualizado com sucesso',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
      },
    },
  );

  const users =
    conversationSectorData?.userConversationSectors.map(
      (user) => user.userId,
    ) || [];
  const isMutateButtonDisabled =
    createConversationSector.isLoading || updateConversationSector.isLoading;

  const onSubmit = async (data: SchemaType) => {
    if (isEditing) {
      updateConversationSector.mutate(data as UpdateConversationSectorDto);
    } else {
      createConversationSector.mutate(data as CreateConversationSectorDto);
    }
  };

  return (
    <LoadingScreen
      isLoading={isUsersQueryLoading || isConversationSectorQueryLoading}
    >
      <Box padding="20px">
        <Box display="flex" alignItems="center" gap={4} mb={4}>
          <IconButton
            aria-label="Voltar"
            icon={<ArrowLeftIcon />}
            onClick={() =>
              navigate(appPaths.settings.conversationSectorsManagement.index())
            }
            size="sm"
            variant="outline"
          />
          <Heading size="md">
            {isEditing ? 'Editar Setor de Conversa' : 'Criar Setor de Conversa'}
          </Heading>
        </Box>

        <Tabs index={selectedTab} onChange={(index) => setSelectedTab(index)}>
          <TabList>
            <Tab>Configurações do setor</Tab>
            <Tab isDisabled={!isEditing}>Categorias do setor</Tab>
          </TabList>
          <TabPanels>
            <TabPanel>
              <form onSubmit={handleSubmit(onSubmit)}>
                <Stack spacing={4} mt={4}>
                  <FormControl>
                    <FormLabel>Nome</FormLabel>
                    <Controller
                      name="name"
                      control={control}
                      render={({ field }) => (
                        <Input
                          placeholder="Digite o nome do setor de conversa"
                          {...field}
                          isInvalid={!!errors.name}
                        />
                      )}
                    />
                    <Text color={colors.danger} fontSize="xs">
                      {errors.name?.message}
                    </Text>
                  </FormControl>

                  <FormControl>
                    <FormLabel>Atendentes</FormLabel>
                    <Controller
                      name="userIds"
                      control={control}
                      render={({ field }) => (
                        <InputSelect
                          options={usersOptions}
                          isMulti
                          {...omit(field, 'ref')}
                          value={(field.value || [])
                            .filter((user): user is string => !!user)
                            .map(
                              (user) =>
                                usersOptions.find(
                                  (option) => option.value === user,
                                ) || {
                                  value: user,
                                  label: user,
                                },
                            )}
                          onChange={(value: SelectOption[]) =>
                            field.onChange(value.map((item) => item.value))
                          }
                          placeholder="Selecione os atendentes para este setor de conversa"
                        />
                      )}
                    />
                  </FormControl>

                  <Flex
                    justify={isEditing ? 'space-between' : 'end'}
                    alignItems="center"
                    gap={2}
                  >
                    {isEditing && (
                      <Tooltip
                        label={
                          'Ao apagar este setor, as categorias associadas serão movidas para Geral'
                        }
                      >
                        <Button
                          colorScheme="red"
                          variant="outline"
                          onClick={onDeleteDialogOpen}
                        >
                          Apagar
                        </Button>
                      </Tooltip>
                    )}
                    <Button
                      type="submit"
                      color={colors.white}
                      bgColor={colors.primary}
                      width="fit-content"
                      isLoading={isMutateButtonDisabled}
                    >
                      {isEditing ? 'Atualizar' : 'Criar'}
                    </Button>
                  </Flex>
                </Stack>
              </form>
              {conversationSectorData && (
                <DeleteConversationSectorDialog
                  isOpen={isDeleteDialogOpen}
                  onClose={onDeleteDialogClose}
                  conversationSector={conversationSectorData}
                />
              )}
            </TabPanel>
            <TabPanel>
              {isEditing && conversationSectorData && (
                <ConversationSectorCategoriesManagement
                  conversationSector={conversationSectorData}
                />
              )}
            </TabPanel>
          </TabPanels>
        </Tabs>
      </Box>
    </LoadingScreen>
  );
};
