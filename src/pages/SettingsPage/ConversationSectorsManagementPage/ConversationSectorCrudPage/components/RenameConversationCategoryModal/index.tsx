import {
  Button,
  Flex,
  Input,
  Modal,
  ModalBody,
  ModalCloseButton,
  Modal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>eader,
  ModalOverlay,
  Text,
  useToast,
} from '@chakra-ui/react';
import { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import {
  ConversationCategory,
  ConversationSector,
} from '../../../../../../types/Prisma';
import { apiRoutes } from '../../../../../../constants/api-routes';
import {
  ConversationCategoriesService,
  UpdateConversationCategoryDto,
} from '../../../../../../services/conversation-categories.service';

interface RenameConversationCategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  conversationSector: ConversationSector;
  conversationCategory: ConversationCategory;
}

export const RenameConversationCategoryModal = ({
  isOpen,
  onClose,
  conversationSector,
  conversationCategory,
}: RenameConversationCategoryModalProps) => {
  const [newName, setNewName] = useState<string>(conversationCategory.name);
  const toast = useToast();
  const queryClient = useQueryClient();

  const updateConversationCategoryMutation = useMutation(
    ({
      data,
      conversationCategoryId,
    }: {
      data: UpdateConversationCategoryDto;
      conversationCategoryId: string;
    }) =>
      ConversationCategoriesService.updateConversationCategory(
        data,
        conversationCategoryId,
      ),
    {
      onSuccess: async () => {
        toast({
          title: 'Categoria de conversa renomeada com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        await queryClient.invalidateQueries(
          apiRoutes.getConversationSector(conversationSector.id),
        );
        await queryClient.invalidateQueries(
          apiRoutes.listConversationSectors(),
        );
        onClose();
      },
    },
  );

  const handleChangeName = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setNewName(value);
  };

  const handleClickSave = async () => {
    if (!newName?.length || isSaveButtonDisabled) {
      return;
    }

    updateConversationCategoryMutation.mutate({
      data: {
        name: newName,
      },
      conversationCategoryId: conversationCategory.id,
    });
  };

  const handleKeyDownName = async (
    event: React.KeyboardEvent<HTMLInputElement>,
  ) => {
    if (event.key !== 'Enter') {
      return;
    }
    event.preventDefault();
    handleClickSave();
  };

  const isSaveButtonDisabled =
    !newName?.length || newName === conversationCategory.name;

  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Renomear categoria</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <Text paddingBottom={4}>
            Defina um novo nome para a categoria{' '}
            <b>{conversationCategory.name}</b>.
          </Text>
          <Input
            type="text"
            value={newName}
            onChange={handleChangeName}
            onKeyDown={handleKeyDownName}
            placeholder="Insira um novo nome para a categoria"
          />
        </ModalBody>

        <ModalFooter>
          <Flex width="100%" justify="space-between" gap={4}>
            <Button variant="outline" onClick={onClose}>
              Cancelar
            </Button>
            <Button
              isDisabled={isSaveButtonDisabled}
              isLoading={updateConversationCategoryMutation.isLoading}
              variant="primary"
              onClick={handleClickSave}
            >
              Confirmar
            </Button>
          </Flex>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
