import {
  Table<PERSON><PERSON><PERSON>,
  <PERSON>,
  Thead,
  Tr,
  Th,
  <PERSON><PERSON>,
  Td,
  But<PERSON>,
  Flex,
  Box,
  Text,
  Tooltip,
} from '@chakra-ui/react';
import { ConversationSectorWithIncludes } from '../../../../../../types/ConversationSector';
import { colors } from '../../../../../../constants/colors';
import { MoveConversationCategoryFromSectorModal } from '../MoveConversationCategoryFromSectorModal';
import { ConversationCategory } from '../../../../../../types/Prisma';
import { useState } from 'react';
import { RenameConversationCategoryModal } from '../RenameConversationCategoryModal';
import { DeleteConversationCategoryModal } from '../DeleteConversationCategoryModal';
import { CreateConversationCategoryModal } from '../CreateConversationCategoryModal';
import ButtonIcon from '../../../../../../components/ButtonIcon';
import { FaEdit, FaTrashAlt } from 'react-icons/fa';
import { FaArrowRightArrowLeft } from 'react-icons/fa6';

interface ConversationSectorCategoriesManagementProps {
  conversationSector: ConversationSectorWithIncludes;
}

export const ConversationSectorCategoriesManagement = ({
  conversationSector,
}: ConversationSectorCategoriesManagementProps) => {
  const [selectedConversationCategory, setSelectedConversationCategory] =
    useState<ConversationCategory | null>(null);
  const [
    showCreateConversationCategoryModal,
    setShowCreateConversationCategoryModal,
  ] = useState(false);
  const [
    showRenameConversationCategoryModal,
    setShowRenameConversationCategoryModal,
  ] = useState(false);
  const [
    showMoveConversationCategoryModal,
    setShowMoveConversationCategoryModal,
  ] = useState(false);
  const [
    showDeleteConversationCategoryModal,
    setShowDeleteConversationCategoryModal,
  ] = useState(false);

  const handleClickCreateConversationCategory = () => {
    setShowCreateConversationCategoryModal(true);
  };

  const handleClickRenameConversationCategory = (
    category: ConversationCategory,
  ) => {
    setSelectedConversationCategory(category);
    setShowRenameConversationCategoryModal(true);
  };

  const handleClickMoveConversationCategory = (
    category: ConversationCategory,
  ) => {
    setSelectedConversationCategory(category);
    setShowMoveConversationCategoryModal(true);
  };

  const handleClickDeleteConversationCategory = (
    category: ConversationCategory,
  ) => {
    setSelectedConversationCategory(category);
    setShowDeleteConversationCategoryModal(true);
  };

  return (
    <Box>
      <Flex justifyContent="end">
        <Button
          variant="primary"
          size="md"
          mb={4}
          onClick={handleClickCreateConversationCategory}
        >
          Criar Categoria
        </Button>
      </Flex>
      {conversationSector.categories?.length === 0 ? (
        <Text>Não há categorias associadas a este setor</Text>
      ) : (
        <TableContainer>
          <Table variant="simple">
            <Thead>
              <Tr>
                <Th>Categoria</Th>
                <Th textAlign="center">Quantidade de conversas</Th>
                <Th>Ações</Th>
              </Tr>
            </Thead>
            <Tbody>
              {conversationSector.categories?.map((conversationCategory) => (
                <Tr key={`table-${conversationCategory.id}`}>
                  <Td>{conversationCategory.name}</Td>
                  <Td textAlign="center">
                    {conversationCategory._count.conversations}
                  </Td>
                  <Td>
                    <Flex gap={3}>
                      <Tooltip label="Renomear categoria">
                        <ButtonIcon
                          icon={
                            <FaEdit fontSize="20px" color={colors.darkGrey} />
                          }
                          onClick={() =>
                            handleClickRenameConversationCategory(
                              conversationCategory,
                            )
                          }
                        />
                      </Tooltip>
                      <Tooltip label="Mover categoria">
                        <ButtonIcon
                          icon={
                            <FaArrowRightArrowLeft
                              fontSize="20px"
                              color={colors.darkGrey}
                            />
                          }
                          onClick={() =>
                            handleClickMoveConversationCategory(
                              conversationCategory,
                            )
                          }
                        />
                      </Tooltip>
                      <Tooltip label="Apagar categoria">
                        <ButtonIcon
                          icon={
                            <FaTrashAlt fontSize="20px" color={colors.danger} />
                          }
                          onClick={() =>
                            handleClickDeleteConversationCategory(
                              conversationCategory,
                            )
                          }
                        />
                      </Tooltip>
                    </Flex>
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        </TableContainer>
      )}
      <CreateConversationCategoryModal
        isOpen={showCreateConversationCategoryModal}
        onClose={() => {
          setShowCreateConversationCategoryModal(false);
          setSelectedConversationCategory(null);
        }}
        conversationSector={conversationSector}
      />
      {selectedConversationCategory && (
        <>
          <RenameConversationCategoryModal
            isOpen={showRenameConversationCategoryModal}
            onClose={() => {
              setShowRenameConversationCategoryModal(false);
              setSelectedConversationCategory(null);
            }}
            conversationSector={conversationSector}
            conversationCategory={selectedConversationCategory}
          />
          <MoveConversationCategoryFromSectorModal
            isOpen={showMoveConversationCategoryModal}
            onClose={() => {
              setShowMoveConversationCategoryModal(false);
              setSelectedConversationCategory(null);
            }}
            conversationSector={conversationSector}
            conversationCategory={selectedConversationCategory}
          />
          <DeleteConversationCategoryModal
            isOpen={showDeleteConversationCategoryModal}
            onClose={() => {
              setShowDeleteConversationCategoryModal(false);
              setSelectedConversationCategory(null);
            }}
            conversationSector={conversationSector}
            conversationCategory={selectedConversationCategory}
            conversationCount={3}
          />
        </>
      )}
    </Box>
  );
};
