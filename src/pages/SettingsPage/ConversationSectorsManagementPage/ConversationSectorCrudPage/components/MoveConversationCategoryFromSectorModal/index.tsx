import {
  Button,
  Flex,
  <PERSON>dal,
  <PERSON>dal<PERSON>ody,
  Modal<PERSON>lose<PERSON>utton,
  <PERSON>dal<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>eader,
  ModalOverlay,
  Select,
  Text,
  useToast,
} from '@chakra-ui/react';
import { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import {
  ConversationCategory,
  ConversationSector,
} from '../../../../../../types/Prisma';
import { apiRoutes } from '../../../../../../constants/api-routes';
import { ConversationSectorsService } from '../../../../../../services/conversation-sectors.service';
import {
  ConversationCategoriesService,
  UpdateConversationCategoryDto,
} from '../../../../../../services/conversation-categories.service';

interface MoveConversationCategoryFromSectorModalProps {
  isOpen: boolean;
  onClose: () => void;
  conversationSector: ConversationSector;
  conversationCategory: ConversationCategory;
}

export const MoveConversationCategoryFromSectorModal = ({
  isOpen,
  onClose,
  conversationSector,
  conversationCategory,
}: MoveConversationCategoryFromSectorModalProps) => {
  const [newSectorId, setNewSectorId] = useState<string | null>(null);
  const toast = useToast();
  const queryClient = useQueryClient();

  const { data: sectorsOptions = [] } = useQuery(
    apiRoutes.listConversationSectors(),
    async () => {
      const { data } =
        await ConversationSectorsService.listConversationSectors();
      return data.map((conversationSector) => ({
        value: conversationSector.id,
        label: conversationSector.name,
      }));
    },
  );

  const updateConversationCategoryMutation = useMutation(
    ({
      data,
      conversationCategoryId,
    }: {
      data: UpdateConversationCategoryDto;
      conversationCategoryId: string;
    }) =>
      ConversationCategoriesService.updateConversationCategory(
        data,
        conversationCategoryId,
      ),
    {
      onSuccess: async () => {
        toast({
          title: 'Categoria de conversa atualizada com sucesso para novo setor',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        await queryClient.invalidateQueries(
          apiRoutes.getConversationSector(conversationSector.id),
        );
        await queryClient.invalidateQueries(
          apiRoutes.listConversationSectors(),
        );
        onClose();
      },
    },
  );

  const handleSelectConversationSector = (
    event: React.ChangeEvent<HTMLSelectElement>,
  ) => {
    const selectedSectorId = event.target.value;
    setNewSectorId(selectedSectorId);
  };

  const handleClickSave = async () => {
    if (!newSectorId) {
      return;
    }

    updateConversationCategoryMutation.mutate({
      data: {
        conversationSectorId: newSectorId,
      },
      conversationCategoryId: conversationCategory.id,
    });
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Mover categoria</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <Text paddingBottom={4}>
            Selecione um setor para mover a categoria de conversa{' '}
            <b>{conversationCategory.name}</b>, atualmente associada ao setor{' '}
            <b>{conversationSector.name}</b>.
          </Text>
          <Select
            placeholder="Selecione um setor para mover a categoria"
            onChange={handleSelectConversationSector}
          >
            {sectorsOptions.map((sector) => (
              <option
                key={sector.value}
                value={sector.value}
                disabled={sector.value === conversationSector.id}
              >
                {sector.label}
              </option>
            ))}
          </Select>
        </ModalBody>

        <ModalFooter>
          <Flex width="100%" justify="space-between" gap={4}>
            <Button variant="outline" onClick={onClose}>
              Cancelar
            </Button>
            <Button
              isDisabled={!newSectorId}
              isLoading={updateConversationCategoryMutation.isLoading}
              variant="primary"
              onClick={handleClickSave}
            >
              Confirmar
            </Button>
          </Flex>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
