import {
  Button,
  Flex,
  Input,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  Modal<PERSON>ooter,
  ModalHeader,
  ModalOverlay,
  Text,
  useToast,
} from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { ConversationSector } from '../../../../../../types/Prisma';
import { apiRoutes } from '../../../../../../constants/api-routes';
import {
  ConversationCategoriesService,
  CreateConversationCategoryDto,
} from '../../../../../../services/conversation-categories.service';

interface CreateConversationCategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  conversationSector: ConversationSector;
}

export const CreateConversationCategoryModal = ({
  isOpen,
  onClose,
  conversationSector,
}: CreateConversationCategoryModalProps) => {
  const [name, setName] = useState<string>('');
  const toast = useToast();
  const queryClient = useQueryClient();

  useEffect(() => {
    setName('');
  }, [isOpen]);

  const createConversationCategoryMutation = useMutation(
    (data: CreateConversationCategoryDto) =>
      ConversationCategoriesService.createConversationCategory(data),
    {
      onSuccess: async () => {
        toast({
          title: 'Categoria de conversa criada com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        await queryClient.invalidateQueries(
          apiRoutes.getConversationSector(conversationSector.id),
        );
        await queryClient.invalidateQueries(
          apiRoutes.listConversationSectors(),
        );
        onClose();
      },
    },
  );

  const handleChangeName = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setName(value);
  };

  const handleClickSave = async () => {
    if (!name?.length || isSaveButtonDisabled) {
      return;
    }

    createConversationCategoryMutation.mutate({
      name,
      conversationSectorId: conversationSector.id,
    });
  };

  const handleKeyDownName = async (
    event: React.KeyboardEvent<HTMLInputElement>,
  ) => {
    if (event.key !== 'Enter') {
      return;
    }
    event.preventDefault();
    handleClickSave();
  };

  const isSaveButtonDisabled = !name?.length;

  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Criar categoria</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <Text paddingBottom={4}>
            Defina um novo nome da nova categoria para o setor{' '}
            {conversationSector.name}
          </Text>
          <Input
            type="text"
            value={name}
            onChange={handleChangeName}
            onKeyDown={handleKeyDownName}
            placeholder="Insira um nome para a nova categoria"
          />
        </ModalBody>

        <ModalFooter>
          <Flex width="100%" justify="space-between" gap={4}>
            <Button variant="outline" onClick={onClose}>
              Cancelar
            </Button>
            <Button
              isDisabled={isSaveButtonDisabled}
              isLoading={createConversationCategoryMutation.isLoading}
              variant="primary"
              onClick={handleClickSave}
            >
              Confirmar
            </Button>
          </Flex>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
