import React, { Fragment, useState } from 'react';
import {
  Box,
  Button,
  Flex,
  Heading,
  Table,
  Tag,
  Tbody,
  Td,
  Th,
  The<PERSON>,
  Tooltip,
  Tr,
} from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import { ConversationSectorsService } from '../../../services/conversation-sectors.service';
import { appPaths } from '../../../constants/app-paths';
import LoadingScreen from '../../../components/LoadingScreen';
import { apiRoutes } from '../../../constants/api-routes';
import ButtonIcon from '../../../components/ButtonIcon';
import { FaChevronUp, FaChevronDown, FaEdit } from 'react-icons/fa';
import { ConversationSectorManagementSubTable } from './components/ConversationSectorManagementSubTable';
import { colors } from '../../../constants/colors';

export const ConversationSectorsManagementPage = () => {
  const navigate = useNavigate();
  const [
    showSubTableForConversationSectorId,
    setShowSubTableForConversationSectorId,
  ] = useState<string | null>(null);

  const { data: sectorsData, isLoading: isSectorsQueryLoading } = useQuery(
    apiRoutes.listConversationSectors(),
    async () => {
      const { data } =
        await ConversationSectorsService.listConversationSectors();
      return data;
    },
  );

  const toggleShowSubRowsForConversationSector = (roleId: string) => {
    if (showSubTableForConversationSectorId === roleId) {
      return setShowSubTableForConversationSectorId(null);
    }

    setShowSubTableForConversationSectorId(roleId);
  };

  const handleClickCreateConversationSector = () => {
    navigate(appPaths.settings.conversationSectorsManagement.create());
  };

  const handleClickEditConversationSector = (conversationSectorId: string) => {
    navigate(
      appPaths.settings.conversationSectorsManagement.edit(
        conversationSectorId,
      ),
    );
  };

  return (
    <LoadingScreen isLoading={isSectorsQueryLoading}>
      <Box padding="20px">
        <Flex
          width="100%"
          justifyContent="space-between"
          alignItems="center"
          paddingBottom="16px"
        >
          <Heading paddingBottom="16px" size="md">
            Gerenciar Setores de Conversa
          </Heading>
          <Button
            variant="primary"
            onClick={handleClickCreateConversationSector}
          >
            Criar Setor de Conversa
          </Button>
        </Flex>
        <Table variant="simple">
          <Thead>
            <Tr>
              <Th />
              <Th>Nome</Th>
              <Th>Categorias</Th>
              <Th textAlign="center">Quantidade de Atendentes</Th>
              <Th>Ações</Th>
            </Tr>
          </Thead>
          <Tbody>
            {sectorsData?.map((conversationSector) => {
              const Chevron =
                showSubTableForConversationSectorId === conversationSector.id
                  ? FaChevronUp
                  : FaChevronDown;
              return (
                <Fragment key={conversationSector.id}>
                  <Tr>
                    <Td width="16px">
                      <ButtonIcon
                        tooltipLabel={
                          !!conversationSector.userConversationSectors?.length
                            ? 'Exibir atendente vinculados'
                            : 'Nenhum atendente vinculado'
                        }
                        icon={<Chevron style={{ opacity: 0.5 }} />}
                        onClick={() =>
                          toggleShowSubRowsForConversationSector(
                            conversationSector.id,
                          )
                        }
                        disabled={
                          !conversationSector.userConversationSectors?.length
                        }
                      />
                    </Td>
                    <Td width="300px">{conversationSector.name}</Td>
                    <Td maxWidth="300px" overflowX="auto" whiteSpace="nowrap">
                      {conversationSector.categories?.map((category, index) => (
                        <Tag key={index} colorScheme="purple" mr={2}>
                          {category.name}
                        </Tag>
                      )) || 'Nenhuma categoria vinculada'}
                    </Td>
                    <Td textAlign="center">
                      {conversationSector.userConversationSectors?.length}
                    </Td>
                    <Td>
                      <Tooltip label="Editar setor">
                        <ButtonIcon
                          icon={
                            <FaEdit fontSize="20px" color={colors.darkGrey} />
                          }
                          onClick={() =>
                            handleClickEditConversationSector(
                              conversationSector.id,
                            )
                          }
                        />
                      </Tooltip>
                    </Td>
                  </Tr>
                  {showSubTableForConversationSectorId ===
                    conversationSector.id && (
                    <Tr>
                      <Td colSpan={5} p={0}>
                        <ConversationSectorManagementSubTable
                          conversationSector={conversationSector}
                        />
                      </Td>
                    </Tr>
                  )}
                </Fragment>
              );
            })}
          </Tbody>
        </Table>
      </Box>
    </LoadingScreen>
  );
};
