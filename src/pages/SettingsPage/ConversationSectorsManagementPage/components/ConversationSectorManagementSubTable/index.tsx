import {
  TableContainer,
  Table,
  Thead,
  Tr,
  Th,
  Tbody,
  Td,
} from '@chakra-ui/react';
import { ConversationSectorWithIncludes } from '../../../../../types/ConversationSector';
import { colors } from '../../../../../constants/colors';

interface ConversationSectorManagementSubTableProps {
  conversationSector: ConversationSectorWithIncludes;
}

export const ConversationSectorManagementSubTable = ({
  conversationSector,
}: ConversationSectorManagementSubTableProps) => {
  return (
    <TableContainer>
      <Table variant="simple" bg="#F8F9FB">
        <Thead>
          <Tr height="53px">
            <Th />
            <Th>Atendente</Th>
            <Th>Email</Th>
          </Tr>
        </Thead>
        <Tbody>
          {conversationSector.userConversationSectors.map((relation) => (
            <Tr key={relation.userId} height="53px">
              <Td width="106px" />
              <Td width="300px">{relation.user?.name || relation.userId}</Td>
              <Td maxWidth="300px">{relation.user?.email || '---'}</Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  );
};
