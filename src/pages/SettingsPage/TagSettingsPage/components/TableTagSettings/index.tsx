import {
  Box,
  Checkbox,
  Divider,
  IconButton,
  Table,
  TableContainer,
  Tbody,
  Td,
  Th,
  The<PERSON>,
  Toolt<PERSON>,
  Tr,
} from '@chakra-ui/react';
import { FaEdit, FaTrashAlt } from 'react-icons/fa';
import { useQuery } from 'react-query';
import { apiRoutes } from '../../../../../constants/api-routes';
import { colors } from '../../../../../constants/colors';
import { TagsService } from '../../../../../services/tags.service';
import { useCrudTagModal } from '../../../../../hooks/useCrudTagModal';
import { TagWithCount } from '../../../../../types/Tag';
import { useState } from 'react';

const TableTagSettings = () => {
  const { data: fetchedTags } = useQuery(apiRoutes.listTags(), async () => {
    const { data } = await TagsService.listTags();
    return data;
  });
  const tags = fetchedTags ?? [];
  const selectableTags = tags.filter(
    (tag) => !tag.isUsedInFilter && !tag.isAssociatedWithFlowNode,
  );

  const [selectedTagIds, setSelectedTagIds] = useState<string[]>([]);
  const { openEditModal, openDeleteAlert } = useCrudTagModal();

  const tagDeletionAlert = (tag: TagWithCount) => {
    if (tag.isAssociatedWithFlowNode && tag.isUsedInFilter) {
      return 'Você não pode deletar uma tag associada a um fluxo e que está sendo usada em um filtro';
    }

    if (tag.isUsedInFilter) {
      return 'Você não pode deletar uma tag que está sendo usada em um filtro';
    }

    return tag.isAssociatedWithFlowNode
      ? 'Você não pode deletar uma tag associada a um fluxo'
      : 'Deletar tag';
  };

  const toggleSelectAll = () => {
    if (selectedTagIds.length > 0) {
      setSelectedTagIds([]);
    } else {
      setSelectedTagIds(selectableTags.map((tag) => tag.id));
    }
  };

  const toggleSelectOne = (id: string) => {
    setSelectedTagIds((prev) =>
      prev.includes(id) ? prev.filter((tagId) => tagId !== id) : [...prev, id],
    );
  };

  const isChecked = selectedTagIds.length > 0;
  const isIndeterminate =
    selectedTagIds.length > 0 && selectedTagIds.length < selectableTags.length;

  return (
    <Box>
      <Tooltip label="Deletar Tags">
        <IconButton
          icon={<FaTrashAlt fontSize="18px" />}
          aria-label="Deletar Tags"
          size="sm"
          variant="outline"
          onClick={() => {
            openDeleteAlert(selectedTagIds.join(','));
            setSelectedTagIds([]);
          }}
          mr={4}
          visibility={isChecked ? 'visible' : 'hidden'}
        />
      </Tooltip>
      <Divider orientation="horizontal" mt={2} />
      <TableContainer>
        <Table variant="simple">
          <Thead>
            <Tr>
              <Th>
                <Checkbox
                  isChecked={isChecked}
                  onChange={toggleSelectAll}
                  isIndeterminate={isIndeterminate}
                />
              </Th>
              <Th>Tag</Th>
              <Th>Número de clientes</Th>
              <Th>Ações</Th>
            </Tr>
          </Thead>
          <Tbody>
            {tags?.map((tag) => {
              return (
                <Tr key={tag.id}>
                  <Td>
                    <Tooltip label={tagDeletionAlert(tag)} shouldWrapChildren>
                      <Checkbox
                        isChecked={selectedTagIds.includes(tag.id)}
                        onChange={() => toggleSelectOne(tag.id)}
                        isDisabled={
                          tag.isUsedInFilter || tag.isAssociatedWithFlowNode
                        }
                      />
                    </Tooltip>
                  </Td>
                  <Td fontWeight={'bold'}>{tag.name}</Td>
                  <Td>{tag._count.customerTags}</Td>
                  <Td>
                    <Tooltip label="Editar tag">
                      <IconButton
                        aria-label="Editar tag"
                        icon={<FaEdit color={colors.primary} />}
                        variant="ghost"
                        onClick={() => openEditModal(tag.id)}
                      />
                    </Tooltip>
                    <Tooltip label={tagDeletionAlert(tag)}>
                      <IconButton
                        isDisabled={
                          tag.isAssociatedWithFlowNode || tag.isUsedInFilter
                        }
                        aria-label="Deletar tag"
                        icon={<FaTrashAlt color={colors.danger} />}
                        variant="ghost"
                        onClick={() => {
                          setSelectedTagIds((prev) =>
                            prev.filter((id) => id !== tag.id),
                          );
                          openDeleteAlert(tag.id);
                        }}
                      />
                    </Tooltip>
                  </Td>
                </Tr>
              );
            })}
          </Tbody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default TableTagSettings;
