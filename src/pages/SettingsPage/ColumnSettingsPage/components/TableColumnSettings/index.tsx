import {
  Box,
  Switch,
  Table,
  TableContainer,
  Tbody,
  Td,
  Th,
  Thead,
  Tooltip,
  Tr,
  useToast,
  Text,
} from '@chakra-ui/react';
import { useMutation, useQuery } from 'react-query';
import { CustomersService } from '../../../../../services/customers.service';
import { CompaniesService } from '../../../../../services/companies.service';
import { apiRoutes } from '../../../../../constants/api-routes';

const TableColumnSettings = () => {
  const toast = useToast();

  const { data: columns } = useQuery(
    apiRoutes.listCustomerTableHeaders(),
    async () => {
      const { data } = await CustomersService.listCustomerTableHeaders();
      return data;
    },
  );

  const getTranslatedDataType = (dataType: string) => {
    const translations: Record<string, string> = {
      string: 'TEXTO',
      number: 'NÚMERO',
      boolean: 'BOOLEANO',
      date: 'DATA',
    };
    return translations[dataType];
  };

  const toggleDefaultColumn = useMutation(
    (columnId: string) =>
      CustomersService.toggleDefaultColumnIsActive(columnId),
    {
      onSuccess: ({ data }) => {
        toast({
          title: data.isActive ? 'Coluna Ativada' : 'Coluna Inativada',
          description: `A coluna "${data.name}" foi ${data.isActive ? 'ativada' : 'inativada'} com sucesso.`,
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  const toggleCustomColumn = useMutation(
    (columnId: string) => CompaniesService.toggleColumnIsActive(columnId),
    {
      onSuccess: ({ data }) => {
        toast({
          title: data.isActive ? 'Coluna Ativada' : 'Coluna Inativada',
          description: `A coluna "${data.name}" foi ${data.isActive ? 'ativada' : 'inativada'} com sucesso.`,
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  const nonDisableableColumnsIds = ['name', 'phone_number_id', 'email'];

  const sortedColumns = [...(columns || [])].sort((a, b) => {
    return a.fieldType === 'custom' ? 1 : b.fieldType === 'custom' ? -1 : 0;
  });

  return (
    <TableContainer>
      <Table variant="simple">
        <Thead>
          <Tr>
            <Th>Nome do campo</Th>
            <Th>Tipo</Th>
            <Th>Ativo</Th>
            <Th>Tipo da coluna</Th>
          </Tr>
        </Thead>
        <Tbody>
          {sortedColumns.map((column) => {
            const isCustomColumn = column.fieldType === 'custom';
            const isNonDisableable = nonDisableableColumnsIds.includes(
              column.id,
            );

            return (
              <Tr
                key={column.id}
                bg={isCustomColumn ? 'transparent' : 'gray.100'}
              >
                <Td fontWeight="bold">
                  <Box display="flex" alignItems="center" gap={2}>
                    <Text>{column.header}</Text>
                  </Box>
                </Td>
                <Td>{getTranslatedDataType(column.dataType)}</Td>
                <Td>
                  <Tooltip
                    label="Esta coluna não pode ser desabilitada"
                    hasArrow
                    isDisabled={!isNonDisableable}
                  >
                    <span>
                      <Switch
                        size="md"
                        colorScheme="green"
                        defaultChecked={column.isActive}
                        onChange={async () => {
                          if (isCustomColumn) {
                            await toggleCustomColumn.mutateAsync(column.id);
                          } else {
                            await toggleDefaultColumn.mutateAsync(column.id);
                          }
                        }}
                        isDisabled={isNonDisableable}
                      />
                    </span>
                  </Tooltip>
                </Td>
                <Td color={isCustomColumn ? 'gray.600' : 'gray.700'}>
                  {isCustomColumn ? 'Customizada' : 'Padrão'}
                </Td>
              </Tr>
            );
          })}
        </Tbody>
      </Table>
    </TableContainer>
  );
};

export default TableColumnSettings;
