import {
  Box,
  Button,
  Container,
  Divider,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Stack,
  Switch,
  Text,
  Textarea,
  useBoolean,
  useToast,
} from '@chakra-ui/react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { colors } from '../../../constants/colors';
import { CompaniesService } from '../../../services/companies.service';
import { Company } from '../../../types/Company';

const schema = yup
  .object({
    firstContactMessage: yup.string(),
    name: yup.string().required('Nome é um campo obrigatório'),
    phoneNumber: yup.string().required('Número é um campo obrigatório'),
    isAutomaticSortingActive: yup.boolean(),
    shouldIncludeAttendantNameInMessages: yup.boolean(),
    optoutMessage: yup
      .string()
      .nullable()
      .notOneOf([''], 'A palavra de bloqueio não pode estar vazia'),
  })
  .required();

const GeneralSettingsPage = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    control,
  } = useForm({
    resolver: yupResolver(schema),
  });
  const [isLoading, setIsLoading] = useBoolean();
  const toast = useToast();

  useEffect(() => {
    CompaniesService.getCompanyDetails().then(({ data: company }) => {
      setValue('firstContactMessage', company.firstContactMessage);
      setValue('name', company.name);
      setValue('phoneNumber', company.phoneNumber);
      setValue('isAutomaticSortingActive', company.isAutomaticSortingActive);
      setValue('optoutMessage', company.optoutMessage || 'STOP');
      setValue(
        'shouldIncludeAttendantNameInMessages',
        company.shouldIncludeAttendantNameInMessages,
      );
    });
  }, []);

  async function onSubmit(data: any) {
    setIsLoading.on();
    CompaniesService.updateCompany(data)
      .then(() => {
        toast({
          title: 'Opção salva com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      })
      .finally(() => {
        setIsLoading.off();
      });
  }

  return (
    <Box padding="20px">
      <Container>
        <Heading size="md" mb={5}>
          Dados cadastrais
        </Heading>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Stack spacing={5}>
            <FormControl>
              <FormLabel>Nome da empresa</FormLabel>
              <Input
                placeholder="Nome"
                {...register('name')}
                isInvalid={errors.name?.message}
                disabled
              />
              <Text color={colors.danger} fontSize="xs">
                {errors.name?.message}
              </Text>
            </FormControl>
            <FormControl>
              <FormLabel>Número whatsapp cadastrado</FormLabel>
              <Input
                {...register('phoneNumber')}
                isInvalid={errors.name?.message}
                disabled
              />
              <Text color={colors.danger} fontSize="xs">
                {errors.name?.message}
              </Text>
            </FormControl>
            <FormControl display="flex" justifyContent="space-between">
              <FormLabel mb="0">
                Enviar o nome do atendente na mensagem
              </FormLabel>
              <Controller
                control={control}
                name="shouldIncludeAttendantNameInMessages"
                render={({ field }) => (
                  <Switch
                    {...field}
                    isChecked={!!field.value}
                    onChange={(e) => field.onChange(e.target.checked)}
                    size="md"
                    colorScheme="green"
                  />
                )}
              />
            </FormControl>
            <Divider />
            <Heading size="md">Triagem</Heading>
            <FormControl>
              <FormLabel>Mensagem inicial</FormLabel>
              <Textarea
                placeholder="Digite aqui a mensagem inicial"
                {...register('firstContactMessage')}
                isInvalid={errors.firstContactMessage?.message}
              />
              <Text color={colors.danger} fontSize="xs">
                {errors.firstContactMessage?.message}
              </Text>
            </FormControl>
            <FormControl>
              <FormLabel>Personalizar palavra de bloqueio</FormLabel>
              <Textarea
                placeholder="Digite aqui a sua palavra de bloqueio customizada"
                {...register('optoutMessage')}
                isInvalid={errors.optoutMessage?.message}
              />
              <Text color={colors.danger} fontSize="xs">
                {errors.optoutMessage?.message}
              </Text>
            </FormControl>
            <Flex justify={'flex-end'}>
              <Button
                width="30%"
                isLoading={isLoading}
                color={colors.white}
                bgColor={colors.primary}
                type="submit"
              >
                Salvar
              </Button>
            </Flex>
          </Stack>
        </form>
      </Container>
    </Box>
  );
};

export default GeneralSettingsPage;
