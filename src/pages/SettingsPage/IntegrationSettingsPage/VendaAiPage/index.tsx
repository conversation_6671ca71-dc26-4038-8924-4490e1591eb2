import {
  Box,
  Button,
  Checkbox,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Stack,
  Text,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { useMutation, useQuery } from 'react-query';
import { apiRoutes } from '../../../../constants/api-routes';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { colors } from '../../../../constants/colors';
import {
  IntegrationsService,
  SaveVendaAiCredentialsDto,
  IntegrationsConfigDto,
  SaveIntegrationsConfigDto,
} from '../../../../services/integrations.service';
import AlertDialogBase from '../../../../components/AlertDialog';
import { useState } from 'react';
import { SourceIntegration } from '../../../../types/SourceIntegration';
import IntegrationsTable, { IntegrationsTableColumn } from '../../../../components/IntegrationsTable';

const createSchema = (isEditing: boolean) => yup.object({
  description: yup.string().default('').required('A descrição é obrigatória'),
  tenancyName: yup
    .string()
    .default('')
    .required('O nome da loja é obrigatório'),
  userName: yup
    .string()
    .default('')
    .required('O nome de usuário/e-mail é obrigatório'),
  password: isEditing
    ? yup.string().default('')
    : yup.string().default('').required('A senha é obrigatória'),
  isOrderActive: yup.boolean().default(true),
  isActive: yup.boolean().default(true),
});

const VendaAiPage = () => {
  const toast = useToast();
  const [showForm, setShowForm] = useState(false);
  const [editingIntegrationId, setEditingIntegrationId] = useState<
    string | null
  >(null);

  // Configuração das colunas da tabela - para VendaAi, mostramos apenas pedidos
  const tableColumns: IntegrationsTableColumn[] = [
    { key: 'isOrderActive', label: 'Sinc. de Pedidos', show: true },
    { key: 'isProductActive', label: 'Sinc. de Produtos', show: false },
    { key: 'isAbandonedCartActive', label: 'Sinc. de Carrinhos Abandonados', show: false },
  ];
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    getValues,
    setValue,
    reset,
  } = useForm({
    resolver: yupResolver(createSchema(!!editingIntegrationId)),
  });

  const {
    onOpen: onOpenSaveConfirmation,
    onClose: onCloseSaveConfirmation,
    isOpen: isOpenSaveConfirmation,
  } = useDisclosure();

  const { data: integrationsConfig, refetch: refetchIntegrationsConfig } =
    useQuery(
      apiRoutes.getIntegrationsConfig(SourceIntegration.venda_ai),
      async () => {
        const { data } = await IntegrationsService.getIntegrationsConfig(
          SourceIntegration.venda_ai,
        );
        return data;
      },
    );

  const saveVendaAiCredentials = useMutation(
    (data: SaveIntegrationsConfigDto<SaveVendaAiCredentialsDto>) =>
      IntegrationsService.saveIntegrationConfig(data),
    {
      onSuccess: () => {
        // refetchVendaAiConfig();
        refetchIntegrationsConfig();
        toast({
          title: 'Configurações salvas com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  async function handleSaveData() {
    const passwordValue = getValues('password');
    const config: SaveVendaAiCredentialsDto = {
      userName: getValues('userName') || '',
      tenancyName: getValues('tenancyName') || '',
      password: passwordValue || '',
    };

    const data = {
      id: editingIntegrationId || undefined,
      isOrderActive: getValues('isOrderActive'),
      source: SourceIntegration.venda_ai,
      description: getValues('description'),
      config,
      isActive: getValues('isActive'),
      isAbandonedCartActive: false,
      isProductActive: false,
      companyId: '',
    };
    await saveVendaAiCredentials.mutateAsync(data);

    reset();
    setShowForm(false);
    setEditingIntegrationId(null);
  }

  async function onSubmit() {
    handleSaveData();
  }

  const handleEditIntegration = (integration: IntegrationsConfigDto) => {
    setShowForm(true);
    setValue('description', integration.description);
    setValue('isOrderActive', integration.isOrderActive);
    setValue('isActive', integration.isActive);

    // Carregar os dados de configuração
    const config = integration.config as SaveVendaAiCredentialsDto;
    if (config) {
      setValue('tenancyName', config.tenancyName || '');
      setValue('userName', config.userName || '');
      setValue('password', ''); // Não carregar a senha por segurança
    }

    // Armazenar ID da integração sendo editada
    setEditingIntegrationId(integration.id);
  };

  return (
    <Box>
      <Heading size="md" mb={4}>
        Integração Venda Ai
      </Heading>
      {!showForm ? (
        <Flex justifyContent="flex-end" mb={4}>
          <Button
            colorScheme="green"
            onClick={() => setShowForm(true)}
            isLoading={saveVendaAiCredentials.isLoading}
          >
            Adicionar Nova Integração
          </Button>
        </Flex>
      ) : (
        <Box mb={6}>
          <Heading size="sm" mb={4}>
            {editingIntegrationId
              ? 'Editar Integração'
              : 'Cadastrar Nova Integração'}
          </Heading>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Stack spacing={4} mt={4}>
              <FormControl isRequired>
                <FormLabel>Descrição</FormLabel>
                <Input
                  placeholder="Descrição da integração"
                  {...register('description')}
                  isInvalid={!!errors.description?.message}
                />
                <Text color={colors.danger} fontSize="xs">
                  {errors.description?.message}
                </Text>
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Loja</FormLabel>
                <Input
                  placeholder="Nome da loja"
                  {...register('tenancyName')}
                  isInvalid={!!errors.tenancyName?.message}
                />
                <Text color={colors.danger} fontSize="xs">
                  {errors.tenancyName?.message}
                </Text>
                <Text fontSize="xs" color="gray.500">
                  Informe o nome da loja na plataforma Venda Ai
                </Text>
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Nome de usuário / E-mail</FormLabel>
                <Input
                  placeholder="Nome de usuário ou e-mail"
                  {...register('userName')}
                  isInvalid={!!errors.userName?.message}
                />
                <Text color={colors.danger} fontSize="xs">
                  {errors.userName?.message}
                </Text>
                <Text fontSize="xs" color="gray.500">
                  Informe o nome de usuário ou e-mail utilizado na plataforma
                </Text>
              </FormControl>

              <FormControl isRequired={!editingIntegrationId}>
                <FormLabel>Senha</FormLabel>
                <Input
                  placeholder={editingIntegrationId ? 'Deixe em branco para manter a senha atual' : 'Senha'}
                  type="password"
                  {...register('password')}
                  isInvalid={!!errors.password?.message}
                />
                <Text color={colors.danger} fontSize="xs">
                  {errors.password?.message}
                </Text>
                {editingIntegrationId ? (
                  <Text fontSize="xs" color="gray.500">
                    Deixe em branco para manter a senha atual
                  </Text>
                ) : (
                  <Text fontSize="xs" color="gray.500">
                    Informe a senha de acesso à plataforma Venda Ai
                  </Text>
                )}
              </FormControl>

              <FormControl>
                <Controller
                  name="isOrderActive"
                  control={control}
                  defaultValue={false}
                  render={({ field }) => (
                    <Checkbox
                      isChecked={field.value}
                      onChange={(e) =>
                        setValue('isOrderActive', e.target.checked)
                      }
                    >
                      Sincronizar Pedidos
                    </Checkbox>
                  )}
                />
              </FormControl>

              <FormControl>
                <Controller
                  name="isActive"
                  control={control}
                  defaultValue={true}
                  render={({ field }) => (
                    <Checkbox
                      isChecked={field.value}
                      onChange={(e) => setValue('isActive', e.target.checked)}
                    >
                      Status da Sincronização
                    </Checkbox>
                  )}
                />
              </FormControl>

              <Flex justify="flex-end">
                <Box
                  flexDirection="row"
                  gap="14px"
                  display="flex"
                  alignItems="center"
                >
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setShowForm(false);
                      reset();
                      // Limpar o parâmetro 'code' da URL
                      window.history.replaceState(
                        {},
                        document.title,
                        window.location.pathname,
                      );
                    }}
                  >
                    Cancelar
                  </Button>
                  <Button
                    type="submit"
                    color={colors.white}
                    bgColor={colors.primary}
                    width="fit-content"
                    isLoading={saveVendaAiCredentials.isLoading}
                  >
                    Salvar
                  </Button>
                </Box>
              </Flex>
            </Stack>
          </form>
        </Box>
      )}

      <IntegrationsTable
        integrationsConfig={integrationsConfig}
        sourceIntegration={SourceIntegration.venda_ai}
        columns={tableColumns}
        onEdit={handleEditIntegration}
        isLoading={saveVendaAiCredentials.isLoading}
      />

      <AlertDialogBase
        isOpen={isOpenSaveConfirmation}
        onClose={onCloseSaveConfirmation}
        title="Confirmação"
        onConfirm={() => {
          handleSaveData();
          onCloseSaveConfirmation();
        }}
      >
        Após esta ação será necessário autorizar novamente o aplicativo em sua
        loja; deseja continuar?
      </AlertDialogBase>
    </Box>
  );
};

export default VendaAiPage;
