import {
  Box,
  Button,
  Checkbox,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Stack,
  Text,
  useDisclosure,
  useToast,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Tag,
} from '@chakra-ui/react';
import { useMutation, useQuery } from 'react-query';
import { apiRoutes } from '../../../../constants/api-routes';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { colors } from '../../../../constants/colors';
import {
  IntegrationsService,
  SaveVarejoOnlineCredentialsDto,
  SaveIntegrationsConfigDto,
} from '../../../../services/integrations.service';
import AlertDialogBase from '../../../../components/AlertDialog';
import { useEffect, useState } from 'react';
import { SourceIntegration } from '../../../../types/SourceIntegration';
import { format } from 'date-fns';

const schema = yup.object({
  description: yup.string().default('').required('A descrição é obrigatória'),
  authorization_code: yup.string().default(''),
  isOrderActive: yup.boolean().default(true),
  isActive: yup.boolean().default(true),
});

const VarejoOnlineIntegrationPage = () => {
  const toast = useToast();
  const [showForm, setShowForm] = useState(false);
  const [editingIntegrationId, setEditingIntegrationId] = useState<
    string | null
  >(null);
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    getValues,
    setValue,
    reset,
  } = useForm({
    resolver: yupResolver(schema),
  });

  // Verificar se existe o parâmetro code na URL
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');

    if (code) {
      setShowForm(true);
      setValue('authorization_code', code);
    }
  }, [setValue]);

  const {
    onOpen: onOpenSaveConfirmation,
    onClose: onCloseSaveConfirmation,
    isOpen: isOpenSaveConfirmation,
  } = useDisclosure();

  const { data: varejoOnlineAppInstallationUrl } = useQuery(
    apiRoutes.getVarejoOnlineAppInstallationUrl(),
    async () => {
      const { data } =
        await IntegrationsService.getVarejoOnlineAppInstallationUrl();
      return data;
    },
  );

  const { data: integrationsConfig, refetch: refetchIntegrationsConfig } =
    useQuery(
      apiRoutes.getIntegrationsConfig(SourceIntegration.varejo_online),
      async () => {
        const { data } = await IntegrationsService.getIntegrationsConfig(
          SourceIntegration.varejo_online,
        );
        return data;
      },
    );

  const saveVarejoOnlineCredentials = useMutation(
    (data: SaveIntegrationsConfigDto<SaveVarejoOnlineCredentialsDto>) =>
      IntegrationsService.saveIntegrationConfig(data),
    {
      onSuccess: () => {
        // refetchVarejoOnlineConfig();
        refetchIntegrationsConfig();
        toast({
          title: 'Configurações salvas com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  async function handleSaveData() {
    const data = {
      id: editingIntegrationId || undefined,
      isOrderActive: getValues('isOrderActive'),
      source: SourceIntegration.varejo_online,
      description: getValues('description'),
      config: {
        authorization_code: getValues('authorization_code') || '',
      },
      isActive: getValues('isActive'),
      isAbandonedCartActive: false,
      isProductActive: false,
      companyId: '',
    };
    await saveVarejoOnlineCredentials.mutateAsync(data);

    reset();
    setShowForm(false);
    setEditingIntegrationId(null);

    window.history.replaceState({}, document.title, window.location.pathname);
  }

  async function onSubmit() {
    handleSaveData();
  }

  function handleAuthorizeReviApp() {
    const url = varejoOnlineAppInstallationUrl?.url ?? '';
    window.location.href = url;
  }

  return (
    <Box>
      <Heading size="md" mb={4}>
        Integração Varejo Online
      </Heading>
      {!showForm ? (
        <Flex justifyContent="flex-end" mb={4}>
          <Button
            colorScheme="green"
            onClick={handleAuthorizeReviApp}
            isLoading={saveVarejoOnlineCredentials.isLoading}
          >
            Adicionar Nova Integração
          </Button>
        </Flex>
      ) : (
        <Box mb={6}>
          <Heading size="sm" mb={4}>
            {editingIntegrationId
              ? 'Editar Integração'
              : 'Cadastrar Nova Integração'}
          </Heading>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Stack spacing={4} mt={4}>
              <FormControl isRequired>
                <FormLabel>Descrição</FormLabel>
                <Input
                  placeholder="Descrição da integração"
                  {...register('description')}
                  isInvalid={!!errors.description?.message}
                />
                <Text color={colors.danger} fontSize="xs">
                  {errors.description?.message}
                </Text>
              </FormControl>

              <FormControl>
                <FormLabel>Código de Autorização</FormLabel>
                <Input
                  placeholder="Código de autorização"
                  {...register('authorization_code')}
                  isReadOnly
                  value={getValues('authorization_code') || ''}
                />
                <Text fontSize="xs" color="gray.500">
                  Este código é obtido automaticamente da URL
                </Text>
              </FormControl>

              <FormControl>
                <Controller
                  name="isOrderActive"
                  control={control}
                  defaultValue={false}
                  render={({ field }) => (
                    <Checkbox
                      isChecked={field.value}
                      onChange={(e) =>
                        setValue('isOrderActive', e.target.checked)
                      }
                    >
                      Sincronizar Pedidos
                    </Checkbox>
                  )}
                />
              </FormControl>

              <FormControl>
                <Controller
                  name="isActive"
                  control={control}
                  defaultValue={true}
                  render={({ field }) => (
                    <Checkbox
                      isChecked={field.value}
                      onChange={(e) => setValue('isActive', e.target.checked)}
                    >
                      Status da Sincronização
                    </Checkbox>
                  )}
                />
              </FormControl>

              <Flex justify="flex-end">
                <Box
                  flexDirection="row"
                  gap="14px"
                  display="flex"
                  alignItems="center"
                >
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setShowForm(false);
                      reset();
                      // Limpar o parâmetro 'code' da URL
                      window.history.replaceState(
                        {},
                        document.title,
                        window.location.pathname,
                      );
                    }}
                  >
                    Cancelar
                  </Button>
                  <Button
                    type="submit"
                    color={colors.white}
                    bgColor={colors.primary}
                    width="fit-content"
                    isLoading={saveVarejoOnlineCredentials.isLoading}
                  >
                    Salvar
                  </Button>
                </Box>
              </Flex>
            </Stack>
          </form>
        </Box>
      )}

      <TableContainer mt={6}>
        <Table variant="simple">
          <Thead>
            <Tr>
              <Th>Descrição</Th>
              <Th>Sincronização de Pedidos</Th>
              <Th>Criado em</Th>
              <Th>Status</Th>
              <Th>Ações</Th>
            </Tr>
          </Thead>
          <Tbody>
            {integrationsConfig &&
            Array.isArray(integrationsConfig) &&
            integrationsConfig.length > 0 ? (
              integrationsConfig
                .filter(
                  (integration) =>
                    integration.source === SourceIntegration.varejo_online,
                )
                .map((integration) => (
                  <Tr key={integration.id}>
                    <Td>{integration.description}</Td>
                    <Td>
                      {integration.isOrderActive ? (
                        <Tag colorScheme="green">Ativo</Tag>
                      ) : (
                        <Tag colorScheme="red">Inativo</Tag>
                      )}
                    </Td>
                    <Td>
                      {integration.createdAt
                        ? format(
                            new Date(integration.createdAt),
                            'dd/MM/yyyy HH:mm',
                          )
                        : '-'}
                    </Td>
                    <Td>
                      {integration.isActive ? (
                        <Tag colorScheme="green">Ativo</Tag>
                      ) : (
                        <Tag colorScheme="red">Inativo</Tag>
                      )}
                    </Td>
                    <Td>
                      <Button
                        size="sm"
                        colorScheme="blue"
                        onClick={() => {
                          setShowForm(true);
                          setValue('description', integration.description);
                          setValue('isOrderActive', integration.isOrderActive);
                          setValue('isActive', integration.isActive);
                          // Armazenar ID da integração sendo editada
                          setEditingIntegrationId(integration.id);
                        }}
                      >
                        Editar
                      </Button>
                    </Td>
                  </Tr>
                ))
            ) : (
              <Tr>
                <Td colSpan={5} textAlign="center">
                  Nenhuma integração encontrada
                </Td>
              </Tr>
            )}
          </Tbody>
        </Table>
      </TableContainer>

      <AlertDialogBase
        isOpen={isOpenSaveConfirmation}
        onClose={onCloseSaveConfirmation}
        title="Confirmação"
        onConfirm={() => {
          handleSaveData();
          onCloseSaveConfirmation();
        }}
      >
        Após esta ação será necessário autorizar novamente o aplicativo em sua
        loja; deseja continuar?
      </AlertDialogBase>
    </Box>
  );
};

export default VarejoOnlineIntegrationPage;
