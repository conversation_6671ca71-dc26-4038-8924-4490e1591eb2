import {
  <PERSON>,
  <PERSON>ing,
  TableContainer,
  Table,
  Thead,
  Tr,
  Th,
  Tbody,
  Td,
  Tag,
  Button,
  Flex,
  Divider,
} from '@chakra-ui/react';
import { useQuery } from 'react-query';
import { RolesService } from '../../../services/roles.service';
import LoadingScreen from '../../../components/LoadingScreen';
import { apiRoutes } from '../../../constants/api-routes';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { RoleWithIncludes } from '../../../types/Role';
import { appPaths } from '../../../constants/app-paths';
import { FaChevronDown, FaChevronUp, FaEdit } from 'react-icons/fa';
import ButtonIcon from '../../../components/ButtonIcon';
import { Fragment, useState } from 'react';
import { RolesManagementSubTable } from './components/RolesManagementSubTable';
import { colors } from '../../../constants/colors';

export const RolesManagementPage = ({ show = false }: { show?: boolean }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [showSubTableForRoleId, setShowSubTableForRoleId] = useState<
    string | null
  >(null);

  const { data: rolesData, isLoading: isRolesQueryLoading } = useQuery(
    apiRoutes.listRoles(),
    async (): Promise<RoleWithIncludes[]> => {
      const { data } = await RolesService.listRoles();
      return data;
    },
  );

  const handleCreateRole = () => {
    navigate(appPaths.settings.rolesManagement.create());
  };

  const handleEditRole = (roleId: string) => {
    navigate(appPaths.settings.rolesManagement.edit(roleId));
  };

  const toggleShowSubRowsForRole = (roleId: string) => {
    if (showSubTableForRoleId === roleId) {
      return setShowSubTableForRoleId(null);
    }

    setShowSubTableForRoleId(roleId);
  };

  return (
    <LoadingScreen isLoading={isRolesQueryLoading}>
      <Box padding="20px">
        <Flex
          width="100%"
          justifyContent="space-between"
          alignItems="center"
          paddingBottom="16px"
        >
          <Heading size="md">Gerenciar Cargos</Heading>
          <Button variant="primary" onClick={handleCreateRole}>
            Criar Cargo
          </Button>
        </Flex>
        <Divider orientation="horizontal" mt={2} />
        <TableContainer>
          <Table variant="simple">
            <Thead>
              <Tr>
                <Th />
                <Th>Nome</Th>
                <Th>Módulos Permitidos</Th>
                <Th textAlign="center">Quantidade de Usuários</Th>
                <Th>Ações</Th>
              </Tr>
            </Thead>
            <Tbody>
              {rolesData?.map((role) => {
                const Chevron =
                  showSubTableForRoleId === role.id
                    ? FaChevronUp
                    : FaChevronDown;
                return (
                  <Fragment key={role.id}>
                    <Tr>
                      <Td width="16px">
                        <ButtonIcon
                          tooltipLabel={
                            !!role?.users.length
                              ? 'Exibir usuários vinculados'
                              : 'Nenhum usuário vinculado'
                          }
                          icon={
                            <Chevron
                              style={{
                                opacity: 0.5,
                              }}
                            />
                          }
                          onClick={() => toggleShowSubRowsForRole(role.id)}
                          disabled={!role?.users.length}
                        />
                      </Td>
                      <Td width="300px">{role.name}</Td>
                      <Td maxWidth="500px" overflowX="auto" whiteSpace="nowrap">
                        {role.appModulePermissions.length
                          ? role.appModulePermissions.map((module, index) => (
                              <Tag key={index} colorScheme="purple" mr={2}>
                                {t(`enums.AppModules.${module}`)}
                              </Tag>
                            ))
                          : 'Nenhum módulo permitido'}
                      </Td>
                      <Td textAlign="center">{role.users.length}</Td>
                      <Td>
                        <ButtonIcon
                          icon={
                            <FaEdit fontSize="20px" color={colors.darkGrey} />
                          }
                          onClick={() => handleEditRole(role.id)}
                        />
                      </Td>
                    </Tr>
                    {showSubTableForRoleId === role.id && (
                      <Tr>
                        <Td colSpan={5} p={0}>
                          <RolesManagementSubTable role={role} />
                        </Td>
                      </Tr>
                    )}
                  </Fragment>
                );
              })}
            </Tbody>
          </Table>
        </TableContainer>
      </Box>
    </LoadingScreen>
  );
};
