import {
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogBody,
  AlertDialogFooter,
  Button,
  useToast,
} from '@chakra-ui/react';
import { ComponentProps, useRef } from 'react';
import { useMutation } from 'react-query';
import { useNavigate } from 'react-router-dom';
import { appPaths } from '../../../../../../constants/app-paths';
import { RolesService } from '../../../../../../services/roles.service';
import { Role } from '../../../../../../types/Prisma';

interface DeleteRoleDialogProps
  extends Pick<ComponentProps<typeof AlertDialog>, 'isOpen' | 'onClose'> {
  role: Role;
}

export const DeleteRoleDialog = ({
  role,
  isOpen,
  onClose,
}: DeleteRoleDialogProps) => {
  const toast = useToast();
  const navigate = useNavigate();
  const leastDestructiveRef = useRef<HTMLButtonElement | null>(null);

  const deleteRoleMutation = useMutation(
    () => RolesService.deleteRole(role.id),
    {
      onSuccess: () => {
        toast({
          title: 'Cargo apagado com sucesso',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
        navigate(appPaths.settings.rolesManagement.index());
      },
    },
  );

  const handleClickDelete = () => {
    deleteRoleMutation.mutate();
  };

  return (
    <AlertDialog
      isOpen={isOpen}
      leastDestructiveRef={leastDestructiveRef}
      onClose={onClose}
    >
      <AlertDialogOverlay>
        <AlertDialogContent>
          <AlertDialogHeader fontSize="lg" fontWeight="bold">
            Confirmação de Exclusão
          </AlertDialogHeader>

          <AlertDialogBody>
            Você tem certeza de que deseja excluir o cargo <b>{role.name}</b>?
            Esta ação é irreversível!
          </AlertDialogBody>

          <AlertDialogFooter>
            <Button ref={leastDestructiveRef} onClick={onClose}>
              Cancelar
            </Button>
            <Button
              colorScheme="red"
              onClick={handleClickDelete}
              ml={3}
              isLoading={deleteRoleMutation.isLoading}
            >
              Confirmar
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialogOverlay>
    </AlertDialog>
  );
};
