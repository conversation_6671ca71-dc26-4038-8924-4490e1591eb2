import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Stack,
  Text,
  useToast,
  IconButton,
  Tooltip,
  useDisclosure,
} from '@chakra-ui/react';
import { ArrowLeftIcon } from '@chakra-ui/icons';
import { useMutation, useQuery } from 'react-query';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Controller,
  ControllerRenderProps,
  FieldValues,
  useForm,
} from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { RolesService } from '../../../../services/roles.service';
import { apiRoutes } from '../../../../constants/api-routes';
import { colors } from '../../../../constants/colors';
import InputSelect, { SelectOption } from '../../../../components/InputSelect';
import { useTranslation } from 'react-i18next';
import { AppModule, AppModulesEnum } from '../../../../types/AppModule';
import { UsersService } from '../../../../services/users.service';
import { appPaths } from '../../../../constants/app-paths';
import { User } from '../../../../types/Prisma';
import { CreateRoleDto, UpdateRoleDto } from '../../../../types/Role';
import { omit } from 'lodash';
import LoadingScreen from '../../../../components/LoadingScreen';
import { DeleteRoleDialog } from './components/DeleteRoleDialog';

const MODULES_FILTER_OUT: AppModulesEnum[] = [AppModulesEnum.DEBUG_TOOLS];

const schema = yup.object({
  name: yup.string().required('O nome é obrigatório'),
  appModulePermissions: yup.array().of(yup.string()),
  userIds: yup.array().of(yup.string()),
});

type SchemaType = yup.InferType<typeof schema>;

export const RoleCrudPage = () => {
  const { roleId: id } = useParams<{ roleId?: string }>();
  const navigate = useNavigate();
  const toast = useToast();
  const { t } = useTranslation();
  const {
    isOpen: isDeleteDialogOpen,
    onOpen: onDeleteDialogOpen,
    onClose: onDeleteDialogClose,
  } = useDisclosure();
  const isEditing = !!id;

  const {
    handleSubmit,
    formState: { errors, isDirty },
    setValue,
    control,
  } = useForm<SchemaType>({
    resolver: yupResolver(schema),
    defaultValues: {
      name: '',
      appModulePermissions: [],
      userIds: [],
    },
  });

  const appModuleOptions: SelectOption[] = Object.values(AppModulesEnum)
    .filter((module) => !MODULES_FILTER_OUT.includes(module))
    .map((module) => ({
      value: module,
      label: t(`enums.AppModules.${module}`),
    }));

  const { data: usersOptions = [], isLoading: isUsersQueryLoading } = useQuery(
    apiRoutes.listUsers(),
    async () => {
      const { data } = await UsersService.listUsers();
      return data.map((user: User) => ({
        value: user.id,
        label: `${user.name} (${user.email})`,
      }));
    },
  );

  const { data: roleData, isLoading: isRoleQueryLoading } = useQuery(
    apiRoutes.getRole(id!),
    async () => {
      const { data } = await RolesService.getRole(id!);
      return data;
    },
    {
      enabled: isEditing,
      onSuccess: (data) => {
        setValue('name', data.name || '');
        setValue(
          'appModulePermissions',
          data.appModulePermissions.map((module: AppModule) => module) || [],
        );
        setValue('userIds', data.users.map((user) => user.id) || []);
      },
    },
  );

  const createRole = useMutation(
    (data: CreateRoleDto) => RolesService.createRole(data),
    {
      onSuccess: () => {
        toast({
          title: 'Cargo criado com sucesso',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
        navigate(appPaths.settings.rolesManagement.index());
      },
    },
  );

  const updateRole = useMutation(
    (data: UpdateRoleDto) => RolesService.updateRole(id!, data),
    {
      onSuccess: () => {
        toast({
          title: 'Cargo atualizado com sucesso',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
        navigate(appPaths.settings.rolesManagement.index());
      },
    },
  );

  const users = roleData?.users || [];
  const isDeleteButtonDisabled = users.length > 0;
  const isMutateButtonDisabled = createRole.isLoading || updateRole.isLoading;

  const deleteTooltipLabel = isDirty
    ? 'Você tem alterações pendentes.'
    : 'Não é possível apagar um cargo com usuários associados.';

  const onSubmit = async (data: SchemaType) => {
    if (isEditing) {
      updateRole.mutate(data as UpdateRoleDto);
    } else {
      createRole.mutate(data as CreateRoleDto);
    }
  };

  const handleChangeUse = (
    value: SelectOption[],
    field: ControllerRenderProps<SchemaType, 'userIds'>,
  ) => {
    const newValues = value.map((item) => item.value);
    const removedUsers = roleData?.users?.filter(
      (user) => user && !newValues.includes(user.id),
    );

    if (removedUsers?.length) {
      toast({
        title: 'Operação não permitida',
        description:
          'Não é possível desalocar um usuário de um cargo, apenas realocá-lo para outro cargo.',
        status: 'warning',
        duration: 5000,
        isClosable: true,
      });
      return; // Prevent the change
    }

    field.onChange(newValues);
  };

  return (
    <LoadingScreen isLoading={isUsersQueryLoading || isRoleQueryLoading}>
      <Box padding="20px">
        <Box display="flex" alignItems="center" gap={4} mb={4}>
          <IconButton
            aria-label="Voltar"
            icon={<ArrowLeftIcon />}
            onClick={() => navigate(appPaths.settings.rolesManagement.index())}
            size="sm"
            variant="outline"
          />
          <Heading size="md">
            {isEditing ? 'Editar Cargo' : 'Criar Cargo'}
          </Heading>
        </Box>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Stack spacing={4} mt={4}>
            <FormControl>
              <FormLabel>Nome</FormLabel>
              <Controller
                name="name"
                control={control}
                render={({ field }) => (
                  <Input
                    placeholder="Digite o nome do cargo"
                    {...field}
                    isInvalid={!!errors.name}
                  />
                )}
              />
              <Text color={colors.danger} fontSize="xs">
                {errors.name?.message}
              </Text>
            </FormControl>

            <FormControl>
              <FormLabel>Módulos permitidos</FormLabel>
              <Controller
                name="appModulePermissions"
                control={control}
                render={({ field }) => (
                  <InputSelect
                    options={appModuleOptions}
                    isMulti
                    {...omit(field, 'ref')}
                    value={(field.value || [])
                      .filter((module): module is string => !!module)
                      .map((module) => ({
                        value: module,
                        label: t(`enums.AppModules.${module}`),
                      }))}
                    onChange={(value: SelectOption[]) =>
                      field.onChange(value.map((item) => item.value))
                    }
                    placeholder="Selecione os módulos que este cargo terá acesso"
                  />
                )}
              />
            </FormControl>

            <FormControl>
              <FormLabel>Usuários</FormLabel>
              <Controller
                name="userIds"
                control={control}
                render={({ field }) => (
                  <InputSelect
                    options={usersOptions}
                    isMulti
                    {...omit(field, 'ref')}
                    value={(field.value || [])
                      .filter((user): user is string => !!user)
                      .map(
                        (user) =>
                          usersOptions.find(
                            (option) => option.value === user,
                          ) || {
                            value: user,
                            label: user,
                          },
                      )}
                    onChange={(value: SelectOption[]) =>
                      handleChangeUse(value, field)
                    }
                    placeholder="Selecione os usuários para este cargo"
                  />
                )}
              />
            </FormControl>

            <Flex
              justify={isEditing ? 'space-between' : 'end'}
              alignItems="center"
            >
              {isEditing && (
                <>
                  <Tooltip
                    label={deleteTooltipLabel}
                    isDisabled={!isDeleteButtonDisabled}
                  >
                    <Button
                      colorScheme="red"
                      variant="outline"
                      onClick={onDeleteDialogOpen}
                      isDisabled={isDeleteButtonDisabled}
                    >
                      Apagar
                    </Button>
                  </Tooltip>
                  {roleData && (
                    <DeleteRoleDialog
                      isOpen={isDeleteDialogOpen}
                      onClose={onDeleteDialogClose}
                      role={roleData}
                    />
                  )}
                </>
              )}
              <Button
                type="submit"
                color={colors.white}
                bgColor={colors.primary}
                width="fit-content"
                isLoading={isMutateButtonDisabled}
              >
                {isEditing ? 'Atualizar' : 'Criar'}
              </Button>
            </Flex>
          </Stack>
        </form>
      </Box>
    </LoadingScreen>
  );
};
