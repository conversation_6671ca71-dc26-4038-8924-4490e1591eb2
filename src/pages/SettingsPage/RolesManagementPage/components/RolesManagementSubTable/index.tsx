import {
  TableContainer,
  <PERSON>,
  Thead,
  Tr,
  Th,
  Tbody,
  Td,
} from '@chakra-ui/react';
import { RoleWithIncludes } from '../../../../../types/Role';
import { colors } from '../../../../../constants/colors';

interface RolesManagementSubTableProps {
  role: RoleWithIncludes;
}

export const RolesManagementSubTable = ({
  role,
}: RolesManagementSubTableProps) => {
  return (
    <TableContainer>
      <Table variant="simple" bg="#F8F9FB">
        <Thead>
          <Tr height="53px">
            <Th />
            <Th>Usuário</Th>
            <Th>Email</Th>
          </Tr>
        </Thead>
        <Tbody>
          {role.users.map((user) => (
            <Tr key={user.id} height="53px">
              <Td width="106px" />
              <Td width="300px">{user.name}</Td>
              <Td maxWidth="300px">{user.email}</Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  );
};
