import { Box, Button, Flex } from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import { appPaths } from '../../../constants/app-paths';
import TableEmailTemplates from './components/TableEmailTemplates';

const EmailTemplates = () => {
  const navigate = useNavigate();

  return (
    <Box paddingTop={'16px'}>
      <Flex justifyContent={'end'}>
        <Button
          variant="primary"
          onClick={() =>
            navigate(appPaths.messageTemplates.email.createEmailTemplate())
          }
        >
          + criar template
        </Button>
      </Flex>
      <TableEmailTemplates />
    </Box>
  );
};

export default EmailTemplates;
