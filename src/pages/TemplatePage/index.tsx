import {
  Box,
  Flex,
  <PERSON>ing,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
} from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import EmailTemplates from './EmailTemplate';
import MessageTemplates from './MessageTemplate';

interface TemplatesPageProps {
  tab?: string;
}

const TemplatesPage = ({ tab = 'mensagem' }: TemplatesPageProps) => {
  const [channel, setChannel] = useState('mensagem');
  const [tabIndex, setTabIndex] = useState(tab === 'email' ? 1 : 0);

  useEffect(() => {
    setTabIndex(tab === 'email' ? 1 : 0);
  }, [tab]);

  return (
    <Box padding="20px">
      <Flex width="100%" justifyContent="space-between">
        <Heading mb={5}>Templates</Heading>
      </Flex>
      <Tabs index={tabIndex} onChange={setTabIndex}>
        <TabList>
          <Tab>Mensagem</Tab>
          <Tab>Email</Tab>
        </TabList>
        <TabPanels>
          <TabPanel>
            <MessageTemplates />
          </TabPanel>
          <TabPanel>
            <EmailTemplates />
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Box>
  );
};

export default TemplatesPage;
