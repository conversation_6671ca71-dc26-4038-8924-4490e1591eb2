import { useState, useEffect, useRef } from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  Text,
  VStack,
  Flex,
  Icon,
  useColorModeValue,
  Box,
  Input,
  FormControl,
  FormLabel,
  Select,
  FormHelperText,
  Alert,
  AlertIcon,
  Radio,
  RadioGroup,
  Stack,
} from '@chakra-ui/react';
import {
  FiUsers,
  FiShoppingBag,
  FiUpload,
  FiFile,
  FiInfo,
} from 'react-icons/fi';
import { motion } from 'framer-motion';
import { useMutation, useQuery } from 'react-query';
import { CustomerUploadHeadersEnum } from '../../../../types/CustomerUploadHeadersEnum';
import { apiRoutes } from '../../../../constants/api-routes';
import { CompaniesService } from '../../../../services/companies.service';
import {
  UploadCustomersDto,
  CustomersService,
  BirthDateFormat,
} from '../../../../services/customers.service';
import { MixpanelService } from '../../../../services/mixpanel.service';
import {
  UploadOrdersDto,
  OrdersService,
} from '../../../../services/orders.service';
import { updateRefetchKey } from '../../../../state/campaignCreationSlice';
import { CompanyDefinedFieldTableEnum } from '../../../../types/CompanyDefinedField';
import { CompanyDefinedField } from '../../../../types/Prisma';
import { FileUtils } from '../../../../utils/file-utils';
import { useDispatch } from 'react-redux';
import { colors } from '../../../../constants/colors';

const MotionBox = motion(Box);

interface UploadFileModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const UploadFileModal = ({ isOpen, onClose }: UploadFileModalProps) => {
  const dispatch = useDispatch();
  const [headerOptions, setHeaderOptions] = useState<string[]>([]);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadType, setUploadType] = useState<null | 'customers' | 'orders'>(
    null,
  );
  const [mappedHeaders, setMappedHeaders] = useState<{
    [key in CustomerUploadHeadersEnum]?: string;
  }>({});

  const [selectedFormat, setSelectedFormat] = useState('');
  const [isDragging, setIsDragging] = useState(false);
  const [fileName, setFileName] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dateLabels = ['Dia', 'Mes', 'Ano'];
  const [showDateFormatOptions, setShowDateFormatOptions] = useState<boolean>();

  const [customDateFormat, setCustomDateFormat] = useState({
    firstPart: 'DD',
    secondPart: 'MM',
    thirdPart: 'YYYY',
  });

  const modalBgColor = useColorModeValue('white', 'gray.800');
  const cardBg = useColorModeValue('white', 'gray.700');
  const hoverBg = useColorModeValue('gray.50', 'gray.600');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const dropAreaBg = useColorModeValue('gray.50', 'gray.700');
  const dropAreaBorderColor = useColorModeValue('gray.200', 'gray.600');
  const activeBorderColor = useColorModeValue('blue.300', 'blue.500');
  const optionsBg = useColorModeValue('gray.50', 'gray.600');

  const uploadCustomers = useMutation(
    (uploadCustomersDto: UploadCustomersDto) =>
      CustomersService.uploadCustomers(uploadCustomersDto),
    {
      onSuccess: () => {
        MixpanelService.track('import-customers');
        dispatch(updateRefetchKey());
        handleClose();
      },
    },
  );
  const uploadOrders = useMutation(
    (uploadOrdersDto: UploadOrdersDto) =>
      OrdersService.uploadOrders(uploadOrdersDto),
    {
      onSuccess: () => {
        MixpanelService.track('import-orders');
        dispatch(updateRefetchKey());
        handleClose();
      },
    },
  );
  const { data: fieldsToMap = [], refetch } = useQuery(
    apiRoutes.listCompanyDefinedFields(CompanyDefinedFieldTableEnum.CUSTOMERS),
    async () => {
      const res = await CompaniesService.listCompanyDefinedFields(
        CompanyDefinedFieldTableEnum.CUSTOMERS,
      );
      return res.data;
    },
    {
      select(data) {
        const customFields = data.map((field: CompanyDefinedField) => ({
          label: field.name,
          value: field.name,
          isRequired: false,
        }));
        return [
          {
            label: 'Nome',
            value: CustomerUploadHeadersEnum.NAME,
            isRequired: true,
          },
          {
            label: 'Telefone',
            value: CustomerUploadHeadersEnum.PHONE_NUMBER,
            isRequired: true,
          },
          {
            label: 'Email',
            value: CustomerUploadHeadersEnum.EMAIL,
            isRequired: false,
          },
          ...customFields,
        ];
      },
    },
  );

  useEffect(() => {
    if (isOpen) {
      refetch();
    }
  }, [isOpen, refetch]);

  async function handleChangeFile(file: File) {
    setSelectedFile(file);

    if (uploadType === 'customers') {
      const headers: string[] = await FileUtils.getHeadersFromSheet(file);
      setHeaderOptions(headers);
    }
  }

  function handleClose() {
    setHeaderOptions([]);
    setSelectedFile(null);
    setMappedHeaders({});
    setUploadType(null);
    setShowDateFormatOptions(false);
    setSelectedFormat('');
    setFileName('');
    onClose();
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      setFileName(files[0].name);
      handleChangeFile(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      setFileName(files[0].name);
      handleChangeFile(files[0]);
    }
  };

  function handleChangeHeader(fieldName: string, value: string) {
    setMappedHeaders((prev) => ({
      ...prev,
      [fieldName]: value || undefined,
    }));

    if (fieldName === 'birthDate' && value) {
      setShowDateFormatOptions(true);
    }
  }

  const predefinedFormats: Record<string, BirthDateFormat> = {
    'dd/mm/yyyy': { firstPart: 'DD', secondPart: 'MM', thirdPart: 'YYYY' },
    'yyyy-mm-dd': { firstPart: 'YYYY', secondPart: 'MM', thirdPart: 'DD' },
  };

  useEffect(() => {
    if (selectedFormat !== 'custom' && predefinedFormats[selectedFormat]) {
      setCustomDateFormat(predefinedFormats[selectedFormat]);
    }
  }, [selectedFormat]);

  const handleCustomChange = (
    key: keyof typeof customDateFormat,
    value: string,
  ) => {
    setCustomDateFormat((prev) => {
      const updatedParts = Object.keys(prev).reduce(
        (acc, partKey) => {
          const typedKey = partKey as keyof typeof prev;
          acc[typedKey] = prev[typedKey] === value ? '' : prev[typedKey];
          return acc;
        },
        {} as typeof prev,
      );

      return { ...updatedParts, [key]: value };
    });
  };

  async function handleSubmit() {
    if (!selectedFile) return;

    const headers = {
      ...mappedHeaders,
      birthDateFormat:
        selectedFormat === 'custom'
          ? customDateFormat
          : predefinedFormats[selectedFormat],
    };

    if (uploadType === 'customers') {
      await uploadCustomers.mutateAsync({
        file: selectedFile,
        mappedHeaders: headers,
      });
      handleClose();
    } else if (uploadType === 'orders') {
      await uploadOrders.mutateAsync({
        file: selectedFile,
      });
      handleClose();
    }
  }

  const renderUploadTypeSelection = () => (
    <VStack spacing={6} align="stretch">
      <Text textAlign="center" fontSize="lg" mb={2}>
        Selecione o tipo de dados que deseja importar
      </Text>

      <Flex
        direction={{ base: 'column', md: 'row' }}
        justify="center"
        align="center"
        gap={4}
      >
        <MotionBox
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          as="button"
          onClick={() => setUploadType('customers')}
          p={6}
          borderRadius="lg"
          borderWidth="1px"
          borderColor={borderColor}
          bg={cardBg}
          boxShadow="md"
          _hover={{ bg: hoverBg }}
          width={{ base: '100%', md: '220px' }}
          textAlign="center"
          transition="all 0.2s"
        >
          <VStack spacing={4}>
            <Icon as={FiUsers} boxSize={10} color={colors.primary} />
            <Text fontWeight="medium" fontSize="lg">
              Dados cadastrais
            </Text>
            <Text fontSize="sm" color="gray.500">
              Importe dados de clientes de uma planilha
            </Text>
          </VStack>
        </MotionBox>

        <MotionBox
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          as="button"
          onClick={() => setUploadType('orders')}
          p={6}
          borderRadius="lg"
          borderWidth="1px"
          borderColor={borderColor}
          bg={cardBg}
          boxShadow="md"
          _hover={{ bg: hoverBg }}
          width={{ base: '100%', md: '220px' }}
          textAlign="center"
          transition="all 0.2s"
        >
          <VStack spacing={4}>
            <Icon as={FiShoppingBag} boxSize={10} color="green.500" />
            <Text fontWeight="medium" fontSize="lg">
              Histórico de vendas
            </Text>
            <Text fontSize="sm" color="gray.500">
              Importe o histórico de pedidos dos clientes
            </Text>
          </VStack>
        </MotionBox>
      </Flex>
    </VStack>
  );

  const renderFileSelection = () => (
    <VStack spacing={6} align="stretch">
      <Text textAlign="center" fontSize="lg" mb={2}>
        {uploadType === 'customers'
          ? 'Selecione o arquivo com dados de clientes'
          : 'Selecione o arquivo com histórico de vendas'}
      </Text>

      <MotionBox
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Box
          as="div"
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          bg={dropAreaBg}
          borderWidth="2px"
          borderStyle="dashed"
          borderColor={isDragging ? activeBorderColor : dropAreaBorderColor}
          borderRadius="lg"
          p={10}
          textAlign="center"
          transition="all 0.2s"
          _hover={{ borderColor: activeBorderColor }}
        >
          <Input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept="text/csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
            display="none"
          />

          <VStack spacing={4}>
            <Icon
              as={FiUpload}
              boxSize="50px"
              color={isDragging ? colors.primaryMedium : colors.primary}
            />

            <Text fontSize="lg" fontWeight="medium">
              {fileName ? fileName : 'Arraste e solte seu arquivo aqui'}
            </Text>

            <Text color="gray.500" fontSize="sm">
              ou
            </Text>

            <Button
              colorScheme="blue"
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              leftIcon={<FiFile />}
            >
              Selecionar arquivo
            </Button>
          </VStack>
        </Box>
      </MotionBox>
    </VStack>
  );

  const renderHeaderMapping = () => (
    <VStack spacing={6} align="stretch">
      <MotionBox
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <VStack spacing={5} align="stretch">
          <Text fontSize="md" fontWeight="medium" mb={1}>
            Associe as colunas correspondentes para importação
          </Text>

          <Box
            borderWidth="1px"
            borderColor={borderColor}
            borderRadius="md"
            p={4}
            bg={cardBg}
          >
            {fieldsToMap.map((field) => (
              <FormControl
                key={field.value}
                isRequired={field.isRequired}
                mb={4}
                isInvalid={field.isRequired && !field.value}
              >
                <Flex
                  direction={{ base: 'column', md: 'row' }}
                  align={{ base: 'start', md: 'center' }}
                  justify="space-between"
                  gap={2}
                >
                  <FormLabel
                    width={{ base: '100%', md: '30%' }}
                    mb={{ base: 1, md: 0 }}
                    fontWeight="medium"
                  >
                    {field.label}
                  </FormLabel>

                  <Select
                    placeholder="Selecione uma coluna"
                    onChange={(e) =>
                      handleChangeHeader(field.value, e.target.value)
                    }
                  >
                    {headerOptions?.map((option, index) => (
                      <option key={index} value={option}>
                        {option}
                      </option>
                    ))}
                  </Select>
                </Flex>
              </FormControl>
            ))}

            <FormControl mb={4}>
              <Flex
                direction={{ base: 'column', md: 'row' }}
                align={{ base: 'start', md: 'center' }}
                justify="space-between"
                gap={2}
              >
                <FormLabel
                  width={{ base: '100%', md: '30%' }}
                  mb={{ base: 1, md: 0 }}
                  fontWeight="medium"
                >
                  Data de Nascimento
                </FormLabel>

                <Select
                  placeholder="Selecione uma coluna"
                  onChange={(e) => {
                    handleChangeHeader('birthDate', e.target.value);
                    setShowDateFormatOptions(true);
                  }}
                >
                  {headerOptions?.map((option, index) => (
                    <option key={index} value={option}>
                      {option}
                    </option>
                  ))}
                </Select>
              </Flex>
              <FormHelperText ml={{ base: 0, md: '30%' }} mt={1}>
                Após selecionar, configure o formato da data
              </FormHelperText>
            </FormControl>

            <FormControl mb={0}>
              <Flex
                direction={{ base: 'column', md: 'row' }}
                align={{ base: 'start', md: 'center' }}
                justify="space-between"
                gap={2}
              >
                <FormLabel
                  width={{ base: '100%', md: '30%' }}
                  mb={{ base: 1, md: 0 }}
                  fontWeight="medium"
                >
                  TAG
                </FormLabel>

                <Input
                  placeholder="Adicionar tag"
                  onChange={(e) =>
                    handleChangeHeader('tags', e.target.value.trim())
                  }
                />
              </Flex>
              <FormHelperText ml={{ base: 0, md: '30%' }} mt={1}>
                Adicione tags para categorizar seus clientes
              </FormHelperText>
            </FormControl>
          </Box>
        </VStack>
      </MotionBox>

      {showDateFormatOptions && (
        <MotionBox
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
          overflow="hidden"
        >
          <Box
            p={6}
            borderWidth="1px"
            borderColor={borderColor}
            borderRadius="lg"
            boxShadow="md"
            bg={cardBg}
          >
            <Alert status="info" variant="left-accent" borderRadius="md" mb={4}>
              <AlertIcon />
              <VStack align="start" spacing={1}>
                <Text lineHeight="short" fontSize="sm">
                  Armazenamos as datas no formato AAAA-MM-DD (ex: 1998-04-22).
                </Text>
                <Text lineHeight="short" fontSize="sm">
                  Se suas datas estão em outro formato, você pode convertê-las
                  automaticamente.
                </Text>
              </VStack>
            </Alert>

            <Text fontSize="md" fontWeight="bold" mb={3} color="gray.700">
              Escolha a ordem da data
            </Text>

            <Text fontSize="sm" color="gray.500" mb={4}>
              O delimitador não importa! Nosso sistema reconhece automaticamente
              qualquer separador como "/", "-", "." ou até espaços.
            </Text>

            <RadioGroup
              value={selectedFormat}
              onChange={setSelectedFormat}
              mb={4}
            >
              <Box p={4} borderWidth="1px" borderRadius="lg" bg={optionsBg}>
                <Text fontSize="md" fontWeight="bold" mb={3} color="gray.700">
                  Ordens comuns
                </Text>

                <Stack spacing={4} direction="column">
                  <Radio value="yyyy-mm-dd" colorScheme="blue">
                    Ano | Mês | Dia{' '}
                    <Text as="span" fontSize="sm" color="gray.500">
                      (ex: 2024-12-31)
                    </Text>
                  </Radio>

                  <Radio value="dd/mm/yyyy" colorScheme="blue">
                    Dia | Mês | Ano{' '}
                    <Text as="span" fontSize="sm" color="gray.500">
                      (ex: 31/12/2024)
                    </Text>
                  </Radio>

                  <Radio value="custom" colorScheme="blue">
                    Outro formato{' '}
                    <Text as="span" fontSize="sm" color="gray.500">
                      (configuração manual)
                    </Text>
                  </Radio>

                  {selectedFormat === 'custom' && (
                    <Box mt={2} ml={6}>
                      <Text
                        fontSize="sm"
                        fontWeight="medium"
                        mb={2}
                        color="gray.600"
                      >
                        Configure seu próprio formato:
                      </Text>

                      <Stack direction="row" spacing={2} align="center">
                        <FormControl w="33%">
                          <Select
                            size="sm"
                            value={customDateFormat.firstPart}
                            onChange={(e) =>
                              handleCustomChange('firstPart', e.target.value)
                            }
                          >
                            <option value="">Selecione</option>
                            {dateLabels.map((option) => (
                              <option key={option} value={option}>
                                {option}
                              </option>
                            ))}
                          </Select>
                        </FormControl>

                        <FormControl w="33%">
                          <Select
                            size="sm"
                            value={customDateFormat.secondPart}
                            onChange={(e) =>
                              handleCustomChange('secondPart', e.target.value)
                            }
                          >
                            <option value="">Selecione</option>
                            {dateLabels.map((option) => (
                              <option key={option} value={option}>
                                {option}
                              </option>
                            ))}
                          </Select>
                        </FormControl>

                        <FormControl w="33%">
                          <Select
                            size="sm"
                            value={customDateFormat.thirdPart}
                            onChange={(e) =>
                              handleCustomChange('thirdPart', e.target.value)
                            }
                          >
                            <option value="">Selecione</option>
                            {dateLabels.map((option) => (
                              <option key={option} value={option}>
                                {option}
                              </option>
                            ))}
                          </Select>
                        </FormControl>
                      </Stack>
                    </Box>
                  )}
                </Stack>
              </Box>
            </RadioGroup>

            <Button
              mt={2}
              colorScheme="blue"
              size="sm"
              onClick={() => setShowDateFormatOptions(false)}
            >
              Confirmar formato
            </Button>
          </Box>
        </MotionBox>
      )}
    </VStack>
  );

  const renderOrdersInfo = () => (
    <MotionBox
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <VStack spacing={6} align="stretch">
        <Alert status="info" variant="left-accent" borderRadius="md">
          <AlertIcon />
          <Text>
            Ao importar pedidos, os clientes associados serão criados
            automaticamente, caso ainda não estejam cadastrados.
          </Text>
        </Alert>

        <Box
          p={6}
          borderWidth="1px"
          borderColor={borderColor}
          borderRadius="lg"
          bg={cardBg}
        >
          <VStack spacing={4} align="start">
            <Box display="flex" alignItems="center">
              <Icon as={FiInfo} mr={2} color="blue.500" />
              <Text fontWeight="medium">Dicas para importação de pedidos:</Text>
            </Box>

            <VStack spacing={3} pl={6} align="start">
              <Text>
                • Certifique-se que cada pedido possua um identificador único
              </Text>
              <Text>
                • Inclua a data do pedido em formato claro (preferência
                AAAA-MM-DD)
              </Text>
              <Text>
                • Adicione o valor total do pedido para relatórios precisos
              </Text>
              <Text>
                • Inclua informações de contato do cliente (telefone
                obrigatório)
              </Text>
            </VStack>
          </VStack>
        </Box>
      </VStack>
    </MotionBox>
  );

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="lg" isCentered>
      <ModalOverlay backdropFilter="blur(2px)" />
      <ModalContent
        bg={modalBgColor}
        boxShadow="xl"
        borderRadius="xl"
        maxW={{ base: '90%', md: '600px' }}
        mx="auto"
      >
        <ModalHeader borderBottomWidth="1px" py={4}>
          {uploadType === null
            ? 'Importação de arquivo'
            : uploadType === 'customers'
              ? 'Importação de clientes'
              : 'Importação de histórico de vendas'}
        </ModalHeader>
        <ModalCloseButton />

        <ModalBody py={6}>
          {uploadType === null
            ? renderUploadTypeSelection()
            : !selectedFile
              ? renderFileSelection()
              : uploadType === 'customers'
                ? renderHeaderMapping()
                : renderOrdersInfo()}
        </ModalBody>

        <ModalFooter borderTopWidth="1px" py={4}>
          <Flex width="100%" justifyContent="space-between">
            {selectedFile && (
              <>
                <Button variant="outline" onClick={() => handleClose()}>
                  Cancelar
                </Button>
                <Button
                  variant="primary"
                  onClick={handleSubmit}
                  isLoading={
                    uploadCustomers.isLoading || uploadOrders.isLoading
                  }
                  loadingText="Importando..."
                >
                  Importar {uploadType === 'customers' ? 'clientes' : 'pedidos'}
                </Button>
              </>
            )}
          </Flex>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default UploadFileModal;
