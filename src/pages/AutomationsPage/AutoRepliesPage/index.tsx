import { <PERSON>, Button, Divider, Flex, Heading } from '@chakra-ui/react';
import React from 'react';
import { colors } from '../../../constants/colors';
import { useCrudAutoReplyModal } from '../../../hooks/useCrudAutoReplyModal';
import TableAutoReplies from './components/TableAutoReplies';

const AutoRepliesPage = () => {
  const { openCreateModal } = useCrudAutoReplyModal();
  return (
    <Box padding="20px">
      <Flex width="100%" justifyContent="space-between">
        <Heading mb={5}>Resposta Automática</Heading>
        <Button variant="primary" onClick={openCreateModal}>
          + Criar Resposta Automática
        </Button>
      </Flex>
      <Divider orientation="horizontal" mt={2} />
      <TableAutoReplies />
    </Box>
  );
};

export default AutoRepliesPage;
