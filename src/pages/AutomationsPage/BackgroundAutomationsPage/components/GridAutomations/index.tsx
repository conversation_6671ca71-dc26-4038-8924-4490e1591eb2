import React, { useMemo, useState } from 'react';
import { Box, Grid, GridItem, useToast } from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import CardAction from '../../../../../components/CardAction';
import { apiRoutes } from '../../../../../constants/api-routes';
import { appPaths } from '../../../../../constants/app-paths';
import { AutomationsService } from '../../../../../services/automations.service';
import { SourceIntegrationLabelsRoutesAndImages } from '../../../../../types/source-integration-labels-routes-and-images';

const GridAutomations = () => {
  const excludedIntegrationKeys = new Set([
    'bling',
    'custom',
    'direct_message',
    'file_import',
    'google_tag_manager',
    'hubspot_crm',
    'unknown',
    'magazord',
    'magento1_ecommerce',
    'magento2_ecommerce',
  ]);

  const [selectedSourceIntegration, setSelectedSourceIntegration] =
    useState('');
  const navigate = useNavigate();
  const toast = useToast();
  const queryClient = useQueryClient();

  const { data: automationTypes } = useQuery(
    apiRoutes.listAutomationTypes(),
    async () => {
      const { data } = await AutomationsService.listAutomationTypes({
        justWithSourceIntegrations: true,
        withAutomations: true,
        orderBy: 'sourceIntegration',
        orderDirection: 'asc',
      });
      return data.filter(
        (automationType) => automationType.sourceIntegration !== null,
      );
    },
  );

  const filteredAutomationTypes = useMemo(
    () =>
      automationTypes?.filter(
        (automationType) =>
          selectedSourceIntegration === '' ||
          automationType.sourceIntegration === selectedSourceIntegration,
      ),
    [selectedSourceIntegration, automationTypes],
  );

  const toggleAutomationTypeMutation = useMutation(
    async ({
      automationTypeId,
      automationId,
    }: {
      automationTypeId: string;
      automationId: string;
    }) => {
      const { data } = await AutomationsService.toggleAutomation(automationId);

      return { automationTypeId: automationTypeId, isActive: data.isActive };
    },
    {
      onSuccess: (data) => {
        toast({
          title: 'Automação atualizada com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        queryClient.setQueryData(
          apiRoutes.listAutomationTypes(),
          (old: any) => {
            return old?.map((automationType: any) => {
              if (automationType.id === data.automationTypeId) {
                automationType.automations[0].isActive = data.isActive;
              }
              return automationType;
            });
          },
        );
      },
    },
  );

  const handleCreateAutomation = (automationTypeId: string) => {
    navigate(
      appPaths.automations.backgroundAutomations.createAutomationByType(
        automationTypeId,
      ),
    );
  };

  const handleEditAutomation = (
    automationTypeId: string,
    automationId: string,
  ) => {
    navigate(
      appPaths.automations.backgroundAutomations.editAutomationByType(
        automationTypeId,
        automationId,
      ),
    );
  };

  const handleViewResults = (automationId: string) => {
    navigate(appPaths.automations.backgroundAutomations.details(automationId));
  };

  const handleToggleAutomation = async (automationType: any) => {
    await toggleAutomationTypeMutation.mutateAsync({
      automationTypeId: automationType.id,
      automationId: automationType.automations[0].id,
    });
  };

  const handleSourceIntegrationChange = (
    event: React.ChangeEvent<HTMLSelectElement>,
  ) => {
    setSelectedSourceIntegration(event.target.value);
  };

  return (
    <>
      <Box mb={4}>
        <Box
          as="select"
          onChange={handleSourceIntegrationChange}
          border="1px"
          borderColor="gray.300"
          borderRadius="md"
          p={2}
          width="100%"
        >
          <option value="">Todos</option>
          {Object.entries(SourceIntegrationLabelsRoutesAndImages)
            .filter(([key]) => !excludedIntegrationKeys.has(key))
            .map(([key, value]) => (
              <option key={key} value={key}>
                {value.label}
              </option>
            ))}
        </Box>
      </Box>

      <Grid templateColumns="repeat(auto-fill, minmax(300px, 1fr))" gap={6}>
        {filteredAutomationTypes?.map((automationType) => {
          const hasAutomation = automationType.automations?.length > 0;
          const isAutomationActive = automationType.automations?.some(
            (element) => element.isActive,
          );
          const sourceIntegration =
            SourceIntegrationLabelsRoutesAndImages[
              automationType.sourceIntegration ?? 'unknown'
            ];

          return (
            <GridItem key={automationType.id}>
              <CardAction
                title={automationType.name}
                subtitle={sourceIntegration.label}
                imgSource={`${process.env.PUBLIC_URL}/${sourceIntegration.image}`}
                primaryActionName="Criar"
                onPrimaryAction={() =>
                  handleCreateAutomation(automationType.id)
                }
                hasSecondaryActions={hasAutomation}
                isActive={isAutomationActive}
                onToggleActive={() => handleToggleAutomation(automationType)}
                onView={
                  hasAutomation
                    ? () => handleViewResults(automationType.automations[0].id)
                    : undefined
                }
                onEdit={
                  hasAutomation
                    ? () =>
                        handleEditAutomation(
                          automationType.id,
                          automationType.automations[0].id,
                        )
                    : undefined
                }
              />
            </GridItem>
          );
        })}
      </Grid>
    </>
  );
};

export default GridAutomations;
