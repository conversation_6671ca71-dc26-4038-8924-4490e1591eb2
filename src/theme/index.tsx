import { extendTheme } from '@chakra-ui/react';
import { colors } from '../constants/colors';

const customTheme = extendTheme({
  colors: {
    primary: {
      main: colors.primary,
    },
  },
  components: {
    Link: {
      baseStyle: {
        color: colors.primary,
      },
    },
    Text: {
      variants: {
        subtitle: {
          color: colors.darkGrey,
        },
      },
    },
    Button: {
      variants: {
        primary: {
          backgroundColor: colors.primary,
          color: colors.white,
        },
      },
    },
    Switch: {
      baseStyle: {
        track: {
          _checked: {
            bg: 'black',
          },
        },
        thumb: {
          bg: 'white',
        },
      },
    },
  },
});

export default customTheme;
