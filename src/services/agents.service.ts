import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { agentRequest } from '../constants/request';

export interface AIReplySuggestions {
  error: boolean;
  message: string;
  suggestions: string[];
}

const getAIReplySuggestions = async (
  conversationId: string,
): Promise<AxiosResponse<AIReplySuggestions>> => {
  return agentRequest.get(apiRoutes.getAIReplySuggestions(conversationId), {
    timeout: 85000,
  });
};

export const AgentsService = {
  getAIReplySuggestions,
};
