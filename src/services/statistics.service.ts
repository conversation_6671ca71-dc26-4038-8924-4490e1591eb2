import { RFMHistoricalChartData } from './../types/RFMHistoricalChartData.d';
import { AxiosPromise, AxiosResponse } from 'axios';
import { request } from '../constants/request';
import { apiRoutes } from '../constants/api-routes';
import { ChartCampaignSalesData } from '../types/ChartCampaignSalesData';
import { RFMSummary } from '../types/RFMSummary';
import { ChartEmailCampaignSalesData } from '../types/ChartEmailCampaignSalesData';

const getTotalWhatsappCampaigns = async (startDate: Date, endDate: Date) => {
  return request.get(apiRoutes.getTotalWhatsappCampaigns(startDate, endDate));
};
const getTotalMesssagesSent = async (startDate: Date, endDate: Date) => {
  return request.get(apiRoutes.getTotalMesssagesSent(startDate, endDate));
};
const getMessagesBounceRate = async (startDate: Date, endDate: Date) => {
  return request.get(apiRoutes.getMessagesBounceRate(startDate, endDate));
};
const getMessagesEngagementRate = async (startDate: Date, endDate: Date) => {
  return request.get(apiRoutes.getMessagesEngagementRate(startDate, endDate));
};
const getChartCampaignPerformance = async (
  campaignId: string,
  timeUnit: 'hour' | 'day',
): Promise<
  AxiosResponse<{
    datetime: string;
    clicked_count: number;
    read_count: number;
    replied_count: number;
    total_orders_value: number;
  }>
> => {
  return request.get(
    apiRoutes.getChartCampaignPerformance(campaignId, timeUnit),
  );
};
const getChartEmailCampaignPerformance = async (
  emailCampaignId: string,
  timeUnit: 'hour' | 'day',
): Promise<
  AxiosResponse<{
    datetime: string;
    clicked_count: number;
    read_count: number;
    total_orders_value: number;
  }>
> => {
  return request.get(
    apiRoutes.getChartEmailCampaignPerformance(emailCampaignId, timeUnit),
  );
};

const getChartCampaignSales = async (
  campaignId: string,
): Promise<AxiosResponse<ChartCampaignSalesData>> => {
  return request.get(apiRoutes.getChartCampaignSales(campaignId));
};

const getChartEmailCampaignSales = async (
  emailCampaignId: string,
): Promise<AxiosResponse<ChartEmailCampaignSalesData>> => {
  return request.get(apiRoutes.getChartEmailCampaignSales(emailCampaignId));
};

export interface OrderAggByCustomerStatistics {
  averageOrderValue: number;
  averageTotalOrders: number;
  averageTotalPurchases: number;
  averageItemValue: number;
}

const getOrderAggByCustomerStatistics = async (
  startOrdersCreatedAt?: string,
  endOrdersCreatedAt?: string,
): Promise<AxiosResponse<OrderAggByCustomerStatistics>> => {
  return request.get(
    apiRoutes.getOrderAggByCustomerStatistics(
      startOrdersCreatedAt,
      endOrdersCreatedAt,
    ),
  );
};

export interface AutomationSalesData {
  received: {
    totalOrders: number;
    totalOrdersValue: number;
    roi: number;
    count: number;
  };
  read: {
    totalOrders: number;
    totalOrdersValue: number;
    roi: number;
    count: number;
  };
  engaged: {
    totalOrders: number;
    totalOrdersValue: number;
    roi: number;
    count: number;
  };
  startDate: Date;
  endDate: Date;
  automationCost: number;
  totalMessagesSent: number;
}

const getChartAutomationSales = async (
  automationId: string,
  startDate: Date,
  endDate: Date,
): Promise<AxiosResponse<AutomationSalesData>> => {
  return request.get(
    apiRoutes.getChartAutomationSales(automationId, startDate, endDate),
  );
};

const getChartAutomationPerformance = async (
  automationId: string,
  startDate: Date,
  endDate: Date,
): Promise<
  AxiosResponse<
    {
      datetime: string;
      orders_count: number;
      total_orders_value: number;
      messages_count: number;
    }[]
  >
> => {
  return request.get(
    apiRoutes.getChartAutomationPerformance(automationId, startDate, endDate),
  );
};

const getReviRevenueSummary = (): AxiosPromise<{
  marketing: number;
  automation: number;
  total: number;
}> => {
  return request.get(apiRoutes.getReviRevenueSummary());
};

const getReviRevenueSummaryByMonth = (): AxiosPromise<
  {
    month: string;
    total_orders_value_from_marketing: number;
    total_orders_value_from_automation: number;
    total_messages_sent_by_marketing: number;
    total_messages_sent_by_automation: number;
  }[]
> => {
  return request.get(apiRoutes.getReviRevenueSummaryByMonth());
};

const getCustomersKpiSummary = (
  startDate: Date,
  endDate: Date,
): AxiosPromise<{
  avgOrderValuePerCustomer: null | number;
  avgTotalOrdersPerCustomer: null | number;
  avgTotalOrdersValuePerCustomer: null | number;
}> => {
  return request.get(apiRoutes.getCustomersKpiSummary(startDate, endDate));
};

const getRFMAnalysis = (maxRecency: number = 365): AxiosPromise<RFMSummary> => {
  return request.get(apiRoutes.getRFMAnalysis(maxRecency));
};

const getRFMAnalysisHistory = (
  numberOfPreviousMonths = 3,
  maxRecency = 365,
): AxiosPromise<RFMHistoricalChartData[]> => {
  return request.get(
    apiRoutes.getRFMAnalysisHistory(numberOfPreviousMonths, maxRecency),
  );
};

export type GetTopCampaignsOrderBy = 'total_orders_value' | 'roi';

const getTopCampaigns = (
  orderBy: GetTopCampaignsOrderBy,
  limit?: number,
): AxiosPromise<
  {
    whatsapp_campaign_id: string;
    filter_criteria: string;
    executed_at: string;
    message_template_name: string;
    total_orders_value: number;
    total_messages_sent: number;
    campaign_cost: number;
    roi: number;
  }[]
> => {
  return request.get(apiRoutes.getTopCampaigns(orderBy, limit));
};

const getOrdersCountByWeekDayAndHour = (): AxiosPromise<
  {
    weekDay: number;
    hour: number;
    count: number;
  }[]
> => {
  return request.get(apiRoutes.getOrdersCountByWeekDayAndHour());
};

export const StatisticsService = {
  getOrderAggByCustomerStatistics,
  getTotalWhatsappCampaigns,
  getTotalMesssagesSent,
  getMessagesBounceRate,
  getMessagesEngagementRate,
  getChartCampaignPerformance,
  getChartEmailCampaignPerformance,
  getChartCampaignSales,
  getChartEmailCampaignSales,
  getChartAutomationSales,
  getChartAutomationPerformance,
  getReviRevenueSummary,
  getCustomersKpiSummary,
  getRFMAnalysis,
  getOrdersCountByWeekDayAndHour,
  getRFMAnalysisHistory,
  getReviRevenueSummaryByMonth,
  getTopCampaigns,
};
